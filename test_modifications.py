#!/usr/bin/env python
"""
Test script to verify the VibroML modifications for structure organization and traceability.

This script tests:
1. Copy structure files to phonon analysis directories
2. Include frequency information in final structure filenames
"""

import os
import tempfile
import shutil
from pathlib import Path

# Import the modified functions
import sys
sys.path.insert(0, '/globalscratch/ucl/modl/rgouvea/VibroML')

from vibroml.utils.phonon_utils import copy_structure_files_to_phonon_analysis_dir, add_frequency_to_structure_filenames


def test_copy_structure_files():
    """Test the copy_structure_files_to_phonon_analysis_dir function."""
    print("Testing copy_structure_files_to_phonon_analysis_dir...")

    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test directories
        final_structures_dir = os.path.join(temp_dir, "final_structures")
        phonon_analysis_dir = os.path.join(temp_dir, "final_phonon_analysis_top_1_energy_m6p0976")
        os.makedirs(final_structures_dir)
        os.makedirs(phonon_analysis_dir)

        # Create test structure files
        test_files = [
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976.cif",
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976.xyz",
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976_primitive.cif",
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976_conventional.cif"
        ]

        for filename in test_files:
            filepath = os.path.join(final_structures_dir, filename)
            with open(filepath, 'w') as f:
                f.write("# Test structure file\n")

        # Test the function
        analysis_id = "top_1_energy_m6p0976"
        copy_structure_files_to_phonon_analysis_dir(phonon_analysis_dir, final_structures_dir, analysis_id)

        # Check if files were copied
        structures_subdir = os.path.join(phonon_analysis_dir, "structure_files")
        assert os.path.exists(structures_subdir), "structure_files subdirectory not created"

        copied_files = os.listdir(structures_subdir)
        assert len(copied_files) == 4, f"Expected 4 files, got {len(copied_files)}"

        print("  ✅ copy_structure_files_to_phonon_analysis_dir test passed")


def test_add_frequency_to_filenames():
    """Test the add_frequency_to_structure_filenames function."""
    print("Testing add_frequency_to_structure_filenames...")

    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test directory
        final_structures_dir = os.path.join(temp_dir, "final_structures")
        os.makedirs(final_structures_dir)

        # Create test structure files
        test_files = [
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976.cif",
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976.xyz",
            "unique_16_iter2_sample50_BaHfSe3_4.530_energy_m6p0339.cif"
        ]

        for filename in test_files:
            filepath = os.path.join(final_structures_dir, filename)
            with open(filepath, 'w') as f:
                f.write("# Test structure file\n")

        # Test with negative frequency
        analysis_id = "top_1_energy_m6p0976"
        softest_frequency = -0.0001
        add_frequency_to_structure_filenames(final_structures_dir, analysis_id, softest_frequency)

        # Check if files were renamed
        files_after = os.listdir(final_structures_dir)
        expected_renamed = [
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976_freqm0p0001THz.cif",
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976_freqm0p0001THz.xyz"
        ]

        for expected_file in expected_renamed:
            assert expected_file in files_after, f"Expected renamed file {expected_file} not found"

        # Test with positive frequency
        analysis_id = "unique_16_energy_m6p0339"
        softest_frequency = 0.0025
        add_frequency_to_structure_filenames(final_structures_dir, analysis_id, softest_frequency)

        # Check if file was renamed
        files_after = os.listdir(final_structures_dir)
        expected_renamed = "unique_16_iter2_sample50_BaHfSe3_4.530_energy_m6p0339_freqp0p0025THz.cif"
        assert expected_renamed in files_after, f"Expected renamed file {expected_renamed} not found"

        print("  ✅ add_frequency_to_structure_filenames test passed")


def main():
    """Run all tests."""
    print("Testing VibroML modifications for structure organization and traceability")
    print("=" * 70)

    try:
        test_copy_structure_files()
        test_add_frequency_to_filenames()

        print("\n🎉 All tests passed!")
        print("\nModifications successfully implemented:")
        print("1. ✅ Copy structure files to phonon analysis directories")
        print("2. ✅ Include frequency information in final structure filenames")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()