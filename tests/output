### File: run_integration_tests.py
#!/usr/bin/env python3
"""
Test runner for VibroML integration tests.

This script runs the comprehensive integration tests for VibroML,
including tests for anisotropic supercells, auto modes, displaced
supercell generation, and units functionality.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path

# Test configuration
CONDA_ENV_PATH = "/globalscratch/ucl/modl/rgouvea/vibroml_env/"
TEST_DIR = Path(__file__).parent


def run_command(cmd, timeout=None):
    """Run a command and return the result."""
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result
    except subprocess.TimeoutExpired:
        print(f"Command timed out: {' '.join(cmd)}")
        return None
    except Exception as e:
        print(f"Error running command: {e}")
        return None


def check_conda_environment():
    """Check if the conda environment is available."""
    print("Checking conda environment...")
    
    cmd = ["conda", "run", "-p", CONDA_ENV_PATH, "python", "-c", "import vibroml; print('VibroML available')"]
    result = run_command(cmd, timeout=30)
    
    if result is None or result.returncode != 0:
        print(f"ERROR: Conda environment not available or VibroML not installed")
        print(f"Expected environment path: {CONDA_ENV_PATH}")
        if result:
            print(f"Error output: {result.stderr}")
        return False
    
    print("✓ Conda environment and VibroML are available")
    return True


def run_unit_tests():
    """Run the unit tests for anisotropic supercell functionality."""
    print("\n" + "="*60)
    print("RUNNING UNIT TESTS")
    print("="*60)
    
    test_files = [
        "test_comprehensive_integration.py::TestAnisotropicSupercellConfiguration",
    ]
    
    for test_file in test_files:
        print(f"\nRunning unit tests in {test_file}...")
        
        cmd = ["python", "-m", "pytest", str(TEST_DIR / test_file), "-v", "-x"]
        result = run_command(cmd, timeout=120)
        
        if result is None or result.returncode != 0:
            print(f"❌ Unit tests failed in {test_file}")
            if result:
                print(f"Error output: {result.stderr}")
                print(f"Standard output: {result.stdout}")
            return False
        else:
            print(f"✓ Unit tests passed in {test_file}")
    
    return True


def run_integration_tests(test_type="all", quick=False):
    """Run the integration tests."""
    print("\n" + "="*60)
    print(f"RUNNING INTEGRATION TESTS ({test_type.upper()})")
    print("="*60)
    
    # Define test categories
    test_categories = {
        "basic": [
            "test_comprehensive_integration.py::TestVibroMLIntegration::test_basic_phonon_calculation",
            "test_comprehensive_integration.py::TestVibroMLIntegration::test_anisotropic_supercells",
        ],
        "auto": [
            "test_auto_traditional.py::TestAutoTraditionalMode::test_auto_traditional_basic",
            "test_auto_ga.py::TestAutoGAMode::test_auto_ga_minimal_parameters",
        ],
        "displaced": [
            "test_displaced_supercell.py::TestDisplacedSupercellGeneration::test_displaced_supercell_gamma_point",
        ],
        "units": [
            "test_units_functionality.py::TestUnitsConversion::test_units_thz_basic",
            "test_units_functionality.py::TestUnitsConversion::test_units_cm1_basic",
        ],
        "all": []  # Will be populated below
    }
    
    # Populate "all" category
    for category_tests in test_categories.values():
        if category_tests:  # Skip the empty "all" list
            test_categories["all"].extend(category_tests)
    
    # Add quick mode tests
    if quick:
        test_categories["quick"] = [
            "test_comprehensive_integration.py::TestVibroMLIntegration::test_basic_phonon_calculation",
            "test_units_functionality.py::TestUnitsConversion::test_units_thz_basic",
        ]
    
    # Select tests to run
    if test_type not in test_categories:
        print(f"Unknown test type: {test_type}")
        print(f"Available types: {list(test_categories.keys())}")
        return False
    
    tests_to_run = test_categories[test_type]
    
    if not tests_to_run:
        print("No tests selected to run")
        return False
    
    print(f"Running {len(tests_to_run)} integration tests...")
    
    # Run each test
    passed = 0
    failed = 0
    
    for test in tests_to_run:
        print(f"\n{'='*40}")
        print(f"Running: {test}")
        print(f"{'='*40}")
        
        cmd = ["python", "-m", "pytest", str(TEST_DIR / test), "-v", "-s", "--tb=short"]
        
        # Set timeout based on test type
        timeout = 180 if quick else 600  # 3 minutes for quick, 10 minutes for full
        
        start_time = time.time()
        result = run_command(cmd, timeout=timeout)
        end_time = time.time()
        
        duration = end_time - start_time
        
        if result is None:
            print(f"❌ Test timed out after {timeout} seconds: {test}")
            failed += 1
        elif result.returncode == 0:
            print(f"✓ Test passed ({duration:.1f}s): {test}")
            passed += 1
        else:
            print(f"❌ Test failed ({duration:.1f}s): {test}")
            print(f"Error output: {result.stderr}")
            print(f"Standard output: {result.stdout}")
            failed += 1
    
    print(f"\n{'='*60}")
    print(f"INTEGRATION TEST RESULTS")
    print(f"{'='*60}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total:  {passed + failed}")
    
    return failed == 0


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="Run VibroML integration tests")
    parser.add_argument(
        "--type", 
        choices=["all", "basic", "auto", "displaced", "units", "quick"],
        default="quick",
        help="Type of tests to run (default: quick)"
    )
    parser.add_argument(
        "--skip-env-check",
        action="store_true",
        help="Skip conda environment check"
    )
    parser.add_argument(
        "--unit-tests-only",
        action="store_true",
        help="Run only unit tests (no integration tests)"
    )
    
    args = parser.parse_args()
    
    print("VibroML Integration Test Runner")
    print("="*60)
    
    # Check conda environment
    if not args.skip_env_check:
        if not check_conda_environment():
            print("\nERROR: Environment check failed. Use --skip-env-check to bypass.")
            return 1
    
    # Run unit tests
    if not run_unit_tests():
        print("\nERROR: Unit tests failed.")
        return 1
    
    # Run integration tests (unless unit-tests-only)
    if not args.unit_tests_only:
        if not run_integration_tests(args.type, quick=(args.type == "quick")):
            print("\nERROR: Integration tests failed.")
            return 1
    
    print("\n" + "="*60)
    print("ALL TESTS COMPLETED SUCCESSFULLY!")
    print("="*60)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

### File: test_auto_ga.py
"""Integration tests for VibroML auto mode with GA method."""

import pytest
import os
import shutil
import subprocess
import json
import time # Import time for consistent logging

# Get the absolute path of the directory containing this script
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# Construct the path to the CIF file, which is inside a subdirectory of the tests folder.
CIF_FILE_PATH = os.path.join(SCRIPT_DIR, "test_structures", "simple_cubic.cif")

# Define the path to the Conda environment
CONDA_ENV_PATH = "/globalscratch/ucl/modl/rgouvea/vibroml_env/"
VIBROML_COMMAND = os.path.join(CONDA_ENV_PATH, "bin", "vibroml")

# Define a base directory for test outputs
TEST_OUTPUT_BASE_DIR = "vibroml_ga_test_outputs"

# --- EXPANDED Simplified parameters for fast GA testing ---
SIMPLIFIED_GA_PARAMS = [
    # Screening parameters - all lists will have length 1 for speed
    "--screen_supercell_ns", "2", # Single supercell for speed
    "--screen_deltas", "0.03", # Single delta
    "--screen_fmax_values", "0.01", # Single fmax

    # GA parameters (minimal values for speed)
    "--ga_population_size", "4",
    "--num_new_points_per_iteration", "2",
    "--ga_mutation_rate", "0.1",

    # Soft mode optimization parameters (minimal values for speed)
    "--soft_mode_max_iterations", "1",
    "--soft_mode_displacement_scales", "1.0",
    "--soft_mode_num_top_structures_to_analyze", "1",
    "--cell_scale_factors", "0.0",
    "--mode2_ratio_scales", "0.0",
    "--num_modes_to_return", "1",
    
    # Other common parameters for speed
    "--no-relax" # Skip initial relaxation for faster testing
]

class TestAutoGAMode:
    """Test auto mode with GA method using simple structures."""

    @classmethod
    def setup_class(cls):
        """Set up the test environment before any tests in this class run."""
        print(f"\n--- Setting up test class: {cls.__name__} ---")
        if os.path.exists(TEST_OUTPUT_BASE_DIR):
            shutil.rmtree(TEST_OUTPUT_BASE_DIR)
        os.makedirs(TEST_OUTPUT_BASE_DIR, exist_ok=True)
        print(f"  Created base test output directory: {TEST_OUTPUT_BASE_DIR}")

        print(f"  Verifying CIF file exists at: {CIF_FILE_PATH}")
        if not os.path.isfile(CIF_FILE_PATH):
            pytest.fail(f"CIF file not found at expected path: {CIF_FILE_PATH}.")
        print(f"  CIF file found: {CIF_FILE_PATH}")

    @classmethod
    def teardown_class(cls):
        """Clean up the test environment after all tests in this class have run."""
        print(f"\n--- Tearing down test class: {cls.__name__} ---")
        if os.path.exists(TEST_OUTPUT_BASE_DIR):
            shutil.rmtree(TEST_OUTPUT_BASE_DIR)
        print("--- Test class teardown complete ---")

    def setup_method(self, method):
        """Set up for each test method."""
        self.test_name = method.__name__
        self.current_test_dir = os.path.join(TEST_OUTPUT_BASE_DIR, self.test_name)
        print(f"\n--- Starting test method: {self.test_name} ---")
        os.makedirs(self.current_test_dir, exist_ok=True)
        
        self.test_cif_path = os.path.join(self.current_test_dir, os.path.basename(CIF_FILE_PATH))
        shutil.copy(CIF_FILE_PATH, self.test_cif_path)
        print(f"  Copied CIF file to: {self.test_cif_path}")

    def teardown_method(self, method):
        """Clean up after each test method."""
        print(f"--- Finished test method: {self.test_name} ---")

    def _run_vibroml_command(self, args, cwd):  
        """Helper to run vibroml command and capture output."""  
        command = [VIBROML_COMMAND] + args  
        full_command_str = ' '.join(command)  
        print(f"  Attempting to execute command: {full_command_str} in {cwd}")  
        print(f"  Current working directory for command: {os.getcwd()}")
        print(f"  Environment PATH: {os.environ.get('PATH')}")
        print(f"  Environment CONDA_PREFIX: {os.environ.get('CONDA_PREFIX')}")
  
        start_time = time.time()  
        try:  
            print("  Starting subprocess.run...")  
            process = subprocess.run(  
                command,  
                cwd=cwd,  
                capture_output=True,  
                text=True,  
                check=True,
                env={"PATH": os.environ["PATH"], "CONDA_PREFIX": CONDA_ENV_PATH}  
            )  
            end_time = time.time()  
            print(f"  Subprocess.run completed in {end_time - start_time:.2f} seconds.")  
            print(f"  Command executed successfully.")  
            print(f"  STDOUT:\n{process.stdout}")  
            if process.stderr:  
                print(f"  STDERR:\n{process.stderr}")  
            return process.stdout  
        except subprocess.CalledProcessError as e:  
            end_time = time.time()  
            print(f"  Subprocess.run failed after {end_time - start_time:.2f} seconds.")  
            print(f"  Command failed with exit code {e.returncode}")  
            print(f"  STDOUT:\n{e.stdout}")  
            print(f"  STDERR:\n{e.stderr}")  
            pytest.fail(f"VibroML command failed: {e}")  
        except FileNotFoundError:  
            print(f"  ERROR: VibroML command not found at {VIBROML_COMMAND}.")  
            pytest.fail(f"VibroML command not found at {VIBROML_COMMAND}. Check CONDA_ENV_PATH.")  
        except subprocess.TimeoutExpired as e:  
            print(f"  ERROR: Command timed out after {e.timeout} seconds.")  
            pytest.fail(f"Command timed out after {e.timeout} seconds.")  
        except Exception as e:  
            end_time = time.time()  
            print(f"  An unexpected error occurred during command execution after {end_time - start_time:.2f} seconds: {e}")  
            pytest.fail(f"Unexpected error during VibroML command execution: {e}")

    def _validate_output_files(self, base_dir, expected_files):
        """Validates that expected files exist."""
        for f in expected_files:
            path = os.path.join(base_dir, f)
            assert os.path.isfile(path), f"Expected file '{path}' not found."

    def _load_json_file(self, file_path):
        """Loads and returns content of a JSON file."""
        assert os.path.isfile(file_path), f"JSON file not found: {file_path}"
        with open(file_path, 'r') as f:
            return json.load(f)

    def test_auto_ga_basic_run_and_workflow_structure(self):
        """
        Test basic auto mode with GA, validating parameters and output structure.
        This combines the basic run check with the workflow structure validation for efficiency.
        """
        print("#### Running test_auto_ga_basic_run_and_workflow_structure ####")
        
        cif_filename = os.path.basename(self.test_cif_path)
        command_args = [
            "--auto", "--method", "ga", "--cif", cif_filename,
            "--fmax", "0.01", "--supercell", "1,1,1",
            "--engine", "mace",
            "--units", "THz"
        ] + SIMPLIFIED_GA_PARAMS
        
        self._run_vibroml_command(command_args, cwd=self.current_test_dir)
        
        # --- Part 1: Validate Basic Execution and Parameters ---
        output_dirs = [d for d in os.listdir(self.current_test_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) == 1, "Expected exactly one output directory."
        output_dir = os.path.join(self.current_test_dir, output_dirs[0])
        
        # MODIFIED: Removed check for "vibroml_workflow.json"
        self._validate_output_files(output_dir, ["initial_settings.json"])
        
        settings_data = self._load_json_file(os.path.join(output_dir, "initial_settings.json"))
        
        assert settings_data["auto"] is True
        assert settings_data["method"] == "ga"
        assert settings_data["units"] == "THz"
        assert settings_data["supercell_dims"] == [1, 1, 1]
        assert settings_data["fmax"] == 0.01
        assert settings_data["screen_supercell_ns"] == [[2, 2, 2]]
        assert settings_data["screen_deltas"] == [0.03]
        assert settings_data["screen_fmax_values"] == [0.01]
        assert settings_data["ga_population_size"] == 4
        assert settings_data["num_new_points_per_iteration"] == 2
        assert settings_data["ga_mutation_rate"] == 0.1
        assert settings_data["soft_mode_max_iterations"] == 1
        assert settings_data["no_relax"] is True
        assert settings_data["engine"] == "mace"

        print("  Basic parameters validated successfully.")

        # --- Part 2: Validate Workflow Structure ---
        # This part assumes the GA workflow runs. If no soft modes are found, these directories might not be created.
        # The test implicitly checks that the command runs without error. If it exits cleanly because no soft modes
        # are found, these assertions might fail. This is acceptable if the test's purpose is to validate the full workflow.
        parameter_sweep_path = os.path.join(output_dir, "N2x2x2_D0.03_F0.01")
        assert os.path.isdir(parameter_sweep_path), f"Expected parameter sweep directory '{parameter_sweep_path}' not found."
        
        self._validate_output_files(parameter_sweep_path, ["settings.json", "phonon_bs_dos_simple_cubic_N2x2x2_D0.03_F0.01.png"])
        
        ga_iteration_path = os.path.join(output_dir, "iter_1")
        assert os.path.isdir(ga_iteration_path), f"Expected GA iteration directory '{ga_iteration_path}' not found."
        
        final_structures_path = os.path.join(output_dir, "final_structures")
        assert os.path.isdir(final_structures_path), f"Expected final_structures directory '{final_structures_path}' not found."
        
        final_phonon_analysis_dirs = [d for d in os.listdir(output_dir) if d.startswith("final_phonon_analysis_top_")]
        assert len(final_phonon_analysis_dirs) > 0, "No final phonon analysis directory found."

        print("  Workflow structure validated successfully.")
        print("#### test_auto_ga_basic_run_and_workflow_structure PASSED ####")

    def test_auto_ga_different_units(self):
        """Test auto mode with GA method using different units."""
        print("#### Running test_auto_ga_different_units ####")
        units_to_test = ["cm-1", "eV"]
        
        for units in units_to_test:
            print(f"  Testing with units: {units}")
            units_test_dir = os.path.join(self.current_test_dir, f"test_{units}")
            os.makedirs(units_test_dir, exist_ok=True)
            
            cif_filename_for_units_test = os.path.basename(self.test_cif_path)
            shutil.copy(self.test_cif_path, os.path.join(units_test_dir, cif_filename_for_units_test))

            command_args = [
                "--auto", "--method", "ga", "--cif", cif_filename_for_units_test,
                "--fmax", "0.01", "--supercell", "1,1,1",
                "--engine", "mace",
                "--units", units
            ] + SIMPLIFIED_GA_PARAMS
            
            self._run_vibroml_command(command_args, cwd=units_test_dir)
            
            output_dirs = [d for d in os.listdir(units_test_dir) if d.startswith("simple_cubic_phonon_output_")]
            assert len(output_dirs) == 1, f"Expected exactly one output directory for units {units}."
            output_dir = os.path.join(units_test_dir, output_dirs[0])
            
            settings_data = self._load_json_file(os.path.join(output_dir, "initial_settings.json"))
            assert settings_data["units"] == units
            assert settings_data["method"] == "ga"
            assert settings_data["no_relax"] is True

        print("#### test_auto_ga_different_units PASSED ####")
    
    def test_ga_vs_traditional_settings_difference(self):
        """Test that GA and traditional methods have different settings and behavior."""
        print("#### Running test_ga_vs_traditional_settings_difference ####")
        
        cif_filename = os.path.basename(self.test_cif_path)

        # Test GA method
        ga_test_dir = os.path.join(self.current_test_dir, "ga_test_case")
        os.makedirs(ga_test_dir, exist_ok=True)
        shutil.copy(self.test_cif_path, os.path.join(ga_test_dir, cif_filename))

        # MODIFIED: Added simplified screening parameters to prevent crash
        ga_args = [
            "--auto", "--method", "ga", "--cif", cif_filename,
            "--fmax", "0.01", "--supercell", "1,1,1",
            "--engine", "mace",
            "--units", "THz",
            "--ga_population_size", "6",
            "--ga_mutation_rate", "0.3",
            "--soft_mode_max_iterations", "1",
            "--no-relax",
            # Added to ensure a stable, fast run for the GA part of this test
            "--screen_supercell_ns", "2",
            "--screen_deltas", "0.03",
            "--screen_fmax_values", "0.01"
            "--num_new_points_per_iteration", "2",  
            "--soft_mode_displacement_scales", "1.0",  
            "--soft_mode_num_top_structures_to_analyze", "1",  
            "--cell_scale_factors", "0.0",  
            "--mode2_ratio_scales", "0.0",  
            "--num_modes_to_return", "1"
        ]
        
        self._run_vibroml_command(ga_args, cwd=ga_test_dir)
        
        # Test traditional method
        trad_test_dir = os.path.join(self.current_test_dir, "traditional_test_case")
        os.makedirs(trad_test_dir, exist_ok=True)
        shutil.copy(self.test_cif_path, os.path.join(trad_test_dir, cif_filename))

        trad_args = [
            "--auto", "--method", "traditional", "--cif", cif_filename,
            "--fmax", "0.01", "--supercell", "1,1,1",
            "--engine", "mace",
            "--units", "THz",
            "--soft_mode_max_iterations", "1",
            "--no-relax",
            # Added to ensure a stable, fast run for the traditional part of this test
            "--screen_supercell_ns", "2",
            "--screen_deltas", "0.03",
            "--screen_fmax_values", "0.01"
        ]
        
        self._run_vibroml_command(trad_args, cwd=trad_test_dir)
        
        # Compare settings files
        ga_output_dirs = [d for d in os.listdir(ga_test_dir) if d.startswith("simple_cubic_phonon_output_")]
        trad_output_dirs = [d for d in os.listdir(trad_test_dir) if d.startswith("simple_cubic_phonon_output_")]
        
        assert len(ga_output_dirs) == 1 and len(trad_output_dirs) == 1, "Output directories not created as expected"
        
        ga_settings_file = os.path.join(ga_test_dir, ga_output_dirs[0], "initial_settings.json")
        trad_settings_file = os.path.join(trad_test_dir, trad_output_dirs[0], "initial_settings.json")
        
        ga_settings = self._load_json_file(ga_settings_file)
        trad_settings = self._load_json_file(trad_settings_file)
        
        assert ga_settings["method"] == "ga"
        assert trad_settings["method"] == "traditional"
        
        assert "ga_population_size" in ga_settings
        assert "ga_mutation_rate" in ga_settings
        assert ga_settings["ga_population_size"] == 6
        assert ga_settings["ga_mutation_rate"] == 0.3

        assert "ga_population_size" not in trad_settings or trad_settings["ga_population_size"] is None
        assert "ga_mutation_rate" not in trad_settings or trad_settings["ga_mutation_rate"] is None

        print("#### test_ga_vs_traditional_settings_difference PASSED ####")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
### File: test_auto_traditional.py
import pytest
import os
import shutil
import subprocess
import json
import time

# Get the absolute path of the directory containing this script
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# Construct the path to the CIF file, which is inside a subdirectory of the tests folder.
CIF_FILE_PATH = os.path.join(SCRIPT_DIR, "test_structures", "simple_cubic.cif")


# Define the path to the Conda environment
CONDA_ENV_PATH = "/globalscratch/ucl/modl/rgouvea/vibroml_env/"
VIBROML_COMMAND = os.path.join(CONDA_ENV_PATH, "bin", "vibroml")

# Define a base directory for test outputs
TEST_OUTPUT_BASE_DIR = "vibroml_test_outputs"

# --- EXPANDED Simplified parameters for fast testing ---
# This now includes overrides for the extensive grid search parameters.
SIMPLIFIED_PARAMS = [
    # Screening parameters - all lists will have length 1
    "--screen_supercell_ns", "2",
    "--screen_deltas", "0.03",
    "--screen_fmax_values", "0.01",

    # Grid search and soft mode parameters (set to minimal values)
    "--soft_mode_max_iterations", "1",
    "--soft_mode_displacement_scales", "1.0",
    "--soft_mode_num_top_structures_to_analyze", "1",
    "--cell_scale_factors", "0.0",
    "--mode2_ratio_scales", "0.0",
    "--num_modes_to_return", "1",
    
    # GA parameters (overridden for safety, though not used in traditional mode)
    "--ga_population_size", "2",
    "--num_new_points_per_iteration", "1",
    "--ga_mutation_rate", "0.1"
]

class TestAutoTraditionalMode:

    @classmethod
    def setup_class(cls):
        """
        Set up the test environment before any tests in this class run.
        """
        print(f"\n--- Setting up test class: {cls.__name__} ---")
        if os.path.exists(TEST_OUTPUT_BASE_DIR):
            shutil.rmtree(TEST_OUTPUT_BASE_DIR)
        os.makedirs(TEST_OUTPUT_BASE_DIR, exist_ok=True)
        print(f"  Created base test output directory: {TEST_OUTPUT_BASE_DIR}")

        print(f"  Verifying CIF file exists at: {CIF_FILE_PATH}")
        if not os.path.isfile(CIF_FILE_PATH):
            pytest.fail(f"CIF file not found at expected path: {CIF_FILE_PATH}.")
        print(f"  CIF file found: {CIF_FILE_PATH}")


    @classmethod
    def teardown_class(cls):
        """
        Clean up the test environment after all tests in this class have run.
        """
        print(f"\n--- Tearing down test class: {cls.__name__} ---")
        if os.path.exists(TEST_OUTPUT_BASE_DIR):
            shutil.rmtree(TEST_OUTPUT_BASE_DIR)
        print("--- Test class teardown complete ---")

    def setup_method(self, method):
        """
        Set up for each test method.
        """
        self.test_name = method.__name__
        self.current_test_dir = os.path.join(TEST_OUTPUT_BASE_DIR, self.test_name)
        print(f"\n--- Starting test method: {self.test_name} ---")
        os.makedirs(self.current_test_dir, exist_ok=True)
        
        self.test_cif_path = os.path.join(self.current_test_dir, os.path.basename(CIF_FILE_PATH))
        shutil.copy(CIF_FILE_PATH, self.test_cif_path)


    def teardown_method(self, method):
        """
        Clean up after each test method.
        """
        print(f"--- Finished test method: {self.test_name} ---")

    def _run_vibroml_command(self, args, cwd):
        """Helper to run vibroml command and capture output."""
        command = [VIBROML_COMMAND] + args
        print(f"  Executing command: {' '.join(command)} in {cwd}")
        start_time = time.time()
        try:
            process = subprocess.run(
                command,
                cwd=cwd,
                capture_output=True,
                text=True,
                check=True,
                env={"PATH": os.environ["PATH"], "CONDA_PREFIX": CONDA_ENV_PATH}
            )
            end_time = time.time()
            print(f"  Command executed successfully in {end_time - start_time:.2f} seconds.")
            print(f"  STDOUT:\n{process.stdout}")
            if process.stderr:
                print(f"  STDERR:\n{process.stderr}")
            return process.stdout
        except subprocess.CalledProcessError as e:
            end_time = time.time()
            print(f"  Command failed after {end_time - start_time:.2f} seconds.")
            print(f"  Command failed with exit code {e.returncode}")
            print(f"  STDOUT:\n{e.stdout}")
            print(f"  STDERR:\n{e.stderr}")
            pytest.fail(f"VibroML command failed: {e}")

    def _validate_output_files(self, base_dir, expected_files):
        """Validates that expected files exist."""
        for f in expected_files:
            path = os.path.join(base_dir, f)
            assert os.path.isfile(path), f"Expected file '{path}' not found."

    def _load_json_file(self, file_path):
        """Loads and returns content of a JSON file."""
        assert os.path.isfile(file_path), f"JSON file not found: {file_path}"
        with open(file_path, 'r') as f:
            return json.load(f)

    def test_basic_auto_mode(self):
        """
        Test the basic auto mode functionality with simplified, fast parameters.
        """
        print("#### Running test_basic_auto_mode ####")
        
        cif_filename = os.path.basename(self.test_cif_path)
        command_args = [
            "--auto", "--method", "traditional", "--cif", cif_filename, 
            "--fmax", "0.01", "--supercell", "1,1,1"
        ] + SIMPLIFIED_PARAMS
        
        self._run_vibroml_command(command_args, cwd=self.current_test_dir)

        output_dirs = [d for d in os.listdir(self.current_test_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) == 1, "Expected exactly one output directory."
        output_dir = os.path.join(self.current_test_dir, output_dirs[0])

        self._validate_output_files(output_dir, ["initial_settings.json"])
        
        settings_data = self._load_json_file(os.path.join(output_dir, "initial_settings.json"))
        
        # --- VALIDATION: Check that ALL our overrides were applied ---
        assert settings_data["supercell_dims"] == [1, 1, 1]
        assert settings_data["fmax"] == 0.01
        
        # --- THIS IS THE CORRECTED LINE ---
        # Your script converts the input '2' to [[2, 2, 2]] before saving.
        assert settings_data["screen_supercell_ns"] == [[2, 2, 2]]
        
        assert settings_data["screen_deltas"] == [0.03]
        assert settings_data["screen_fmax_values"] == [0.01]
        
        # Check the new grid search overrides
        assert settings_data["soft_mode_max_iterations"] == 1
        assert settings_data["soft_mode_displacement_scales"] == [1.0]
        assert settings_data["soft_mode_num_top_structures_to_analyze"] == 1
        assert settings_data["cell_scale_factors"] == [0.0]
        assert settings_data["mode2_ratio_scales"] == [0.0]
        assert settings_data["num_modes_to_return"] == 1
        
        print("#### test_basic_auto_mode PASSED ####")

    def test_anisotropic_supercells(self):
        """
        Test auto mode with anisotropic supercells and fast parameters.
        """
        print("#### Running test_anisotropic_supercells ####")
        
        cif_filename = os.path.basename(self.test_cif_path)
        command_args = [
            "--auto", "--method", "traditional", "--cif", cif_filename, 
            "--fmax", "0.01", "--supercell", "1,2,1"
        ] + SIMPLIFIED_PARAMS

        self._run_vibroml_command(command_args, cwd=self.current_test_dir)

        output_dirs = [d for d in os.listdir(self.current_test_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) == 1, "Expected exactly one output directory."
        output_dir = os.path.join(self.current_test_dir, output_dirs[0])
        
        settings_data = self._load_json_file(os.path.join(output_dir, "initial_settings.json"))
        assert settings_data["supercell_dims"] == [1, 2, 1]
        print("#### test_anisotropic_supercells PASSED ####")

    
### File: test_comprehensive_integration.py
"""Streamlined integration tests for VibroML package with optimized performance."""

import pytest
import os
import tempfile
import shutil
import subprocess
import json
import time
from pathlib import Path

# Test configuration
TEST_CIF_PATH = os.path.join(os.path.dirname(__file__), "test_structures", "simple_cubic.cif")
CONDA_ENV_PATH = "/globalscratch/ucl/modl/rgouvea/vibroml_env/"

# Optimized parameters for fast testing
FAST_TEST_PARAMS = [
    "--supercell", "1,1,1",  # Minimal supercell
    "--delta", "0.05",       # Reasonable delta
    "--fmax", "0.01",        # Reasonable fmax
    "--no-relax"             # Skip relaxation for speed
]

class TestVibroMLIntegration:
    """Streamlined integration tests with performance optimizations."""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for test outputs."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def simple_cif_file(self, temp_dir):
        """Create a simple cubic test structure optimized for fast testing."""
        test_cif_content = """# Simple cubic test structure
data_SimpleCubic
_symmetry_space_group_name_H-M   'P 1'
_cell_length_a   2.50000000
_cell_length_b   2.50000000
_cell_length_c   2.50000000
_cell_angle_alpha   90.00000000
_cell_angle_beta   90.00000000
_cell_angle_gamma   90.00000000
_symmetry_Int_Tables_number   1
_chemical_formula_structural   LiF
_chemical_formula_sum   'Li1 F1'
_cell_volume   15.62500000
_cell_formula_units_Z   1
loop_
 _symmetry_equiv_pos_site_id
 _symmetry_equiv_pos_as_xyz
  1  'x, y, z'
loop_
 _atom_site_type_symbol
 _atom_site_label
 _atom_site_symmetry_multiplicity
 _atom_site_fract_x
 _atom_site_fract_y
 _atom_site_fract_z
 _atom_site_occupancy
  Li  Li0  1  0.00000000  0.00000000  0.00000000  1
  F   F1  1  0.50000000  0.50000000  0.50000000  1"""

        test_cif_path = os.path.join(temp_dir, "simple_cubic.cif")
        with open(test_cif_path, 'w') as f:
            f.write(test_cif_content)

        return test_cif_path

    def run_vibroml_command(self, args, cwd=None, timeout=300):
        """Run a vibroml command and return the result."""
        cmd = ["conda", "run", "-p", CONDA_ENV_PATH, "vibroml"] + args

        try:
            result = subprocess.run(
                cmd,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result
        except subprocess.TimeoutExpired:
            pytest.skip(f"Command timed out after {timeout} seconds")
        except FileNotFoundError:
            pytest.skip("vibroml command not found - conda environment may not be available")
    
    @pytest.mark.slow
    def test_basic_phonon_calculation(self, simple_cif_file, temp_dir):
        """Test basic phonon calculation without auto mode."""
        os.chdir(temp_dir)

        args = ["--cif", simple_cif_file, "--engine", "mace", "--units", "THz"] + FAST_TEST_PARAMS

        result = self.run_vibroml_command(args, cwd=temp_dir)

        # Check that command completed successfully
        assert result.returncode == 0, f"Command failed with stderr: {result.stderr}"

        # Check that output directory was created
        output_dirs = [d for d in os.listdir(temp_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) > 0, "No output directory created"

        output_dir = os.path.join(temp_dir, output_dirs[0])

        # Check that initial settings file exists
        settings_file = os.path.join(output_dir, "initial_settings.json")
        assert os.path.exists(settings_file), "Initial settings file not found"

        # Verify basic settings
        with open(settings_file, 'r') as f:
            settings = json.load(f)
            assert settings["units"] == "THz"
            assert settings["engine"] == "mace"


class TestAnisotropicSupercellConfiguration:
    """Test the enhanced anisotropic supercell configuration."""

    def test_parse_screen_supercell_ns_old_format(self):
        """Test parsing of old format (list of integers)."""
        from vibroml.utils.utils import parse_screen_supercell_ns

        # Old format - list of integers
        old_format = [2, 3, 4]
        result = parse_screen_supercell_ns(old_format)
        expected = [(2, 2, 2), (3, 3, 3), (4, 4, 4)]
        assert result == expected
    
    def test_parse_screen_supercell_ns_new_format(self):
        """Test parsing of new format (list of tuples)."""
        from vibroml.utils.utils import parse_screen_supercell_ns
        
        # New format - list of tuples
        new_format = [[2, 2, 1], [3, 3, 1], [4, 4, 2]]
        result = parse_screen_supercell_ns(new_format)
        expected = [(2, 2, 1), (3, 3, 1), (4, 4, 2)]
        assert result == expected
    
    def test_parse_screen_supercell_ns_mixed_format(self):
        """Test parsing of mixed format (backward compatibility)."""
        from vibroml.utils.utils import parse_screen_supercell_ns
        
        # Mixed format
        mixed_format = [2, [3, 3, 1], 4]
        result = parse_screen_supercell_ns(mixed_format)
        expected = [(2, 2, 2), (3, 3, 1), (4, 4, 4)]
        assert result == expected
    
    def test_parse_screen_supercell_ns_invalid_format(self):
        """Test error handling for invalid formats."""
        from vibroml.utils.utils import parse_screen_supercell_ns
        
        # Invalid formats
        invalid_formats = [
            [2, [3, 3]],  # Wrong number of dimensions
            [2, [3, 3, 0]],  # Zero dimension
            [2, [3, 3, -1]],  # Negative dimension
            [2, "invalid"],  # Wrong type
            "not_a_list"  # Not a list
        ]
        
        for invalid_format in invalid_formats:
            with pytest.raises(ValueError):
                parse_screen_supercell_ns(invalid_format)
    
    def test_default_settings_anisotropic(self):
        """Test that default settings support anisotropic supercells."""
        from vibroml.utils.utils import load_default_settings
        
        settings = load_default_settings()
        screen_supercell_ns = settings.get("screen_supercell_ns", [])
        
        # Should be a list of lists/tuples now
        assert isinstance(screen_supercell_ns, list)
        assert len(screen_supercell_ns) > 0
        
        # Each element should be a list/tuple with 3 elements
        for supercell in screen_supercell_ns:
            assert isinstance(supercell, (list, tuple))
            assert len(supercell) == 3
            assert all(isinstance(x, int) and x > 0 for x in supercell)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
### File: test_displaced_supercell.py
"""Optimized tests for VibroML displaced supercell generation and mode visualization."""

import pytest
import os
import tempfile
import shutil
import subprocess
import json
import re
from pathlib import Path

# Test configuration
CONDA_ENV_PATH = "/globalscratch/ucl/modl/rgouvea/vibroml_env/"

# Fast test parameters for displaced supercell generation
FAST_DISPLACED_PARAMS = [
    "--supercell", "1,1,1",  # Minimal supercell for speed
    "--delta", "0.05",
    "--fmax", "0.01",
    "--no-relax"
]


class TestDisplacedSupercellGeneration:
    """Comprehensive tests for displaced supercell generation and mode visualization."""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for test outputs."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def simple_cif_file(self, temp_dir):
        """Create a simple cubic test structure optimized for fast testing."""
        test_cif_content = """# Simple cubic test structure for displaced supercell testing
data_SimpleCubic
_symmetry_space_group_name_H-M   'P 1'
_cell_length_a   2.50000000
_cell_length_b   2.50000000
_cell_length_c   2.50000000
_cell_angle_alpha   90.00000000
_cell_angle_beta   90.00000000
_cell_angle_gamma   90.00000000
_symmetry_Int_Tables_number   1
_chemical_formula_structural   LiF
_chemical_formula_sum   'Li1 F1'
_cell_volume   15.62500000
_cell_formula_units_Z   1
loop_
 _symmetry_equiv_pos_site_id
 _symmetry_equiv_pos_as_xyz
  1  'x, y, z'
loop_
 _atom_site_type_symbol
 _atom_site_label
 _atom_site_symmetry_multiplicity
 _atom_site_fract_x
 _atom_site_fract_y
 _atom_site_fract_z
 _atom_site_occupancy
  Li  Li0  1  0.00000000  0.00000000  0.00000000  1
  F   F1  1  0.50000000  0.50000000  0.50000000  1"""

        test_cif_path = os.path.join(temp_dir, "simple_cubic.cif")
        with open(test_cif_path, 'w') as f:
            f.write(test_cif_content)

        return test_cif_path

    def run_vibroml_command(self, args, cwd=None, timeout=300):
        """Run a vibroml command and return the result."""
        cmd = ["conda", "run", "-p", CONDA_ENV_PATH, "vibroml"] + args

        try:
            result = subprocess.run(
                cmd,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result
        except subprocess.TimeoutExpired:
            pytest.skip(f"Command timed out after {timeout} seconds")
        except FileNotFoundError:
            pytest.skip("vibroml command not found - conda environment may not be available")

    def validate_displaced_supercell_files(self, mode_gen_dir, expected_q_pattern, expected_mode, expected_displacement=None):
        """Validate that displaced supercell files are generated correctly."""
        # Check that displaced supercell files were created
        cif_files = [f for f in os.listdir(mode_gen_dir) if f.endswith(".cif")]
        xyz_files = [f for f in os.listdir(mode_gen_dir) if f.endswith(".xyz")]

        assert len(cif_files) > 0, "No CIF files generated for displaced supercell"
        assert len(xyz_files) > 0, "No XYZ files generated for displaced supercell"

        # Check filename patterns
        cif_file = cif_files[0]
        assert expected_q_pattern in cif_file, f"Q-point pattern '{expected_q_pattern}' not found in filename: {cif_file}"
        assert f"mode_{expected_mode}" in cif_file, f"Mode index 'mode_{expected_mode}' not found in filename: {cif_file}"

        # Check displacement if specified
        if expected_displacement is not None:
            # Look for displacement pattern in filename
            disp_pattern = f"disp_{expected_displacement:.3f}"
            # Also check for alternative patterns that might be used
            disp_found = any(pattern in cif_file for pattern in [
                disp_pattern,
                f"disp_{expected_displacement}",
                f"disp_{int(expected_displacement*1000):03d}"
            ])
            assert disp_found, f"Displacement pattern not found in filename: {cif_file}. Expected patterns containing {expected_displacement}"

        return cif_files, xyz_files
    
    @pytest.mark.slow
    def test_displaced_supercell_comprehensive(self, simple_cif_file, temp_dir):
        """Comprehensive test for displaced supercell generation at different q-points and modes."""

        # Test cases: (q_point, band_idx, displacement, expected_q_pattern)
        test_cases = [
            ("0.0,0.0,0.0", 0, 0.1, "q_0p000_0p000_0p000"),  # Gamma point
            ("0.5,0.0,0.0", 1, 0.05, "q_0p500_0p000_0p000"),  # X point
        ]

        for q_point, band_idx, displacement, expected_q_pattern in test_cases:
            # Create subdirectory for each test case
            test_name = f"q_{q_point.replace(',', '_').replace('.', 'p')}_mode_{band_idx}"
            test_dir = os.path.join(temp_dir, test_name)
            os.makedirs(test_dir, exist_ok=True)
            os.chdir(test_dir)

            args = [
                "--cif", simple_cif_file,
                "--engine", "mace",
                "--q", q_point,
                "--band_idx", str(band_idx),
                "--displacement", str(displacement),
                "--units", "THz"
            ] + FAST_DISPLACED_PARAMS

            result = self.run_vibroml_command(args, cwd=test_dir)

            # Check that command completed successfully
            assert result.returncode == 0, f"Command failed for q={q_point}, mode={band_idx} with stderr: {result.stderr}"

            # Check that output directory was created
            output_dirs = [d for d in os.listdir(test_dir) if d.startswith("simple_cubic_phonon_output_")]
            assert len(output_dirs) > 0, f"No output directory created for q={q_point}, mode={band_idx}"

            output_dir = os.path.join(test_dir, output_dirs[0])

            # Check that mode generation directory exists
            mode_gen_dirs = [d for d in os.listdir(output_dir) if d.startswith("mode_gen_")]
            assert len(mode_gen_dirs) > 0, f"No mode generation directory found for q={q_point}, mode={band_idx}"

            mode_gen_dir = os.path.join(output_dir, mode_gen_dirs[0])

            # Validate displaced supercell files
            cif_files, xyz_files = self.validate_displaced_supercell_files(
                mode_gen_dir, expected_q_pattern, band_idx, displacement
            )

            # Check that initial settings were saved with correct parameters
            settings_file = os.path.join(output_dir, "initial_settings.json")
            assert os.path.exists(settings_file), f"Initial settings file not found for q={q_point}, mode={band_idx}"

            with open(settings_file, 'r') as f:
                settings = json.load(f)
                assert settings["q"] == q_point
                assert settings["band_idx"] == band_idx
                assert settings["displacement"] == displacement

    def test_displaced_supercell_different_units(self, simple_cif_file, temp_dir):
        """Test displaced supercell generation with different units (optimized)."""
        units_to_test = ["cm-1", "eV"]

        for units in units_to_test:
            # Create subdirectory for each units test
            units_dir = os.path.join(temp_dir, f"test_{units}")
            os.makedirs(units_dir, exist_ok=True)
            os.chdir(units_dir)

            args = [
                "--cif", simple_cif_file,
                "--engine", "mace",
                "--q", "0.0,0.0,0.0",
                "--band_idx", "0",
                "--displacement", "0.1",
                "--units", units
            ] + FAST_DISPLACED_PARAMS

            result = self.run_vibroml_command(args, cwd=units_dir)

            # Check that command completed successfully
            assert result.returncode == 0, f"Command failed for units {units} with stderr: {result.stderr}"

            # Check that output directory was created
            output_dirs = [d for d in os.listdir(units_dir) if d.startswith("simple_cubic_phonon_output_")]
            assert len(output_dirs) > 0, f"No output directory created for units {units}"

            output_dir = os.path.join(units_dir, output_dirs[0])

            # Validate that settings reflect the correct units
            settings_file = os.path.join(output_dir, "initial_settings.json")
            assert os.path.exists(settings_file), f"Initial settings file not found for units {units}"

            with open(settings_file, 'r') as f:
                settings = json.load(f)
                assert settings["units"] == units

    def test_displaced_supercell_anisotropic_supercell(self, simple_cif_file, temp_dir):
        """Test displaced supercell generation with anisotropic supercells."""
        os.chdir(temp_dir)

        args = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--q", "0.0,0.0,0.5",  # Z point
            "--band_idx", "2",     # Third mode
            "--displacement", "0.08",
            "--supercell", "2,3,1",  # Anisotropic supercell
            "--units", "THz"
        ] + FAST_DISPLACED_PARAMS

        result = self.run_vibroml_command(args, cwd=temp_dir)

        # Check that command completed successfully
        assert result.returncode == 0, f"Command failed with stderr: {result.stderr}"

        # Check that output directory was created
        output_dirs = [d for d in os.listdir(temp_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) > 0, "No output directory created"

        output_dir = os.path.join(temp_dir, output_dirs[0])

        # Check that mode generation directory exists
        mode_gen_dirs = [d for d in os.listdir(output_dir) if d.startswith("mode_gen_")]
        assert len(mode_gen_dirs) > 0, "No mode generation directory found"

        mode_gen_dir = os.path.join(output_dir, mode_gen_dirs[0])

        # Validate displaced supercell files
        self.validate_displaced_supercell_files(
            mode_gen_dir, "q_0p000_0p000_0p500", 2, 0.08
        )

        # Check that settings reflect the anisotropic supercell
        settings_file = os.path.join(output_dir, "initial_settings.json")
        assert os.path.exists(settings_file), "Initial settings file not found"

        with open(settings_file, 'r') as f:
            settings = json.load(f)
            assert settings["supercell_dims"] == [2, 3, 1]
    
    @pytest.mark.slow
    def test_displaced_supercell_anisotropic_supercell(self, simple_cif_file, temp_dir):
        """Test displaced supercell generation with anisotropic supercell."""
        os.chdir(temp_dir)
        
        args = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--q", "0.0,0.0,0.5",  # Z point
            "--band_idx", "2",     # Third mode
            "--displacement", "0.08",
            "--supercell", "2,3,1",  # Anisotropic supercell
            "--units", "THz"
        ] + FAST_DISPLACED_PARAMS

        result = self.run_vibroml_command(args, cwd=temp_dir)

        # Check that command completed successfully
        assert result.returncode == 0, f"Command failed with stderr: {result.stderr}"

        # Check that output directory was created
        output_dirs = [d for d in os.listdir(temp_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) > 0, "No output directory created"

        output_dir = os.path.join(temp_dir, output_dirs[0])

        # Check that mode generation directory exists
        mode_gen_dirs = [d for d in os.listdir(output_dir) if d.startswith("mode_gen_")]
        assert len(mode_gen_dirs) > 0, "No mode generation directory found"

        mode_gen_dir = os.path.join(output_dir, mode_gen_dirs[0])

        # Validate displaced supercell files
        self.validate_displaced_supercell_files(
            mode_gen_dir, "q_0p000_0p000_0p500", 2, 0.08
        )

        # Check that settings reflect the anisotropic supercell
        settings_file = os.path.join(output_dir, "initial_settings.json")
        assert os.path.exists(settings_file), "Initial settings file not found"

        with open(settings_file, 'r') as f:
            settings = json.load(f)
            assert settings["supercell_dims"] == [2, 3, 1]
    
    def test_displaced_supercell_parameter_validation(self, simple_cif_file, temp_dir):
        """Test parameter validation for displaced supercell generation (optimized)."""
        os.chdir(temp_dir)

        # Test invalid q-point format (should fail)
        args_invalid_q = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--q", "0.0,0.0",  # Missing third component
            "--band_idx", "0",
            "--displacement", "0.1",
            "--units", "THz"
        ] + FAST_DISPLACED_PARAMS

        result = self.run_vibroml_command(args_invalid_q, cwd=temp_dir)
        # This should fail with an error about invalid q-point format
        assert result.returncode != 0, "Command should fail with invalid q-point format"

    def test_displaced_supercell_workflow_integration(self, simple_cif_file, temp_dir):
        """Test that displaced supercell generation integrates properly with the overall workflow (optimized)."""
        os.chdir(temp_dir)

        # Run displaced supercell generation directly (no need for separate basic run)
        args = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--q", "0.0,0.0,0.0",
            "--band_idx", "0",
            "--displacement", "0.1",
            "--units", "THz"
        ] + FAST_DISPLACED_PARAMS

        result = self.run_vibroml_command(args, cwd=temp_dir)
        assert result.returncode == 0, f"Displaced supercell generation failed: {result.stderr}"

        # Check that output directory was created with expected structure
        output_dirs = [d for d in os.listdir(temp_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) > 0, "No output directory created"

        output_dir = os.path.join(temp_dir, output_dirs[0])

        # Check that the displaced supercell run has the expected structure
        mode_gen_dirs = [d for d in os.listdir(output_dir) if d.startswith("mode_gen_")]
        assert len(mode_gen_dirs) > 0, "No mode generation directory found in displaced supercell output"

        # Verify that both CIF and XYZ files are generated
        mode_gen_dir = os.path.join(output_dir, mode_gen_dirs[0])
        cif_files = [f for f in os.listdir(mode_gen_dir) if f.endswith(".cif")]
        xyz_files = [f for f in os.listdir(mode_gen_dir) if f.endswith(".xyz")]

        assert len(cif_files) > 0, "No CIF files generated"
        assert len(xyz_files) > 0, "No XYZ files generated"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])

### File: test_existing_functions.py
"""Test existing VibroML functions that actually exist in the codebase."""

import os
import tempfile
import pytest
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from ase import Atoms
from ase.build import bulk


class TestExistingUtils:
    """Test existing utility functions."""
    
    def test_parse_supercell_dimensions(self):
        """Test the new parse_supercell_dimensions function we added."""
        from vibroml.utils.utils import parse_supercell_dimensions
        
        # Test various input formats
        assert parse_supercell_dimensions("3") == (3, 3, 3)
        assert parse_supercell_dimensions("2,3,4") == (2, 3, 4)
        assert parse_supercell_dimensions(3) == (3, 3, 3)
        assert parse_supercell_dimensions([2, 3, 4]) == (2, 3, 4)
        assert parse_supercell_dimensions((2, 3, 4)) == (2, 3, 4)
        
        # Test error cases
        with pytest.raises(ValueError):
            parse_supercell_dimensions("1,2")  # Too few dimensions
        with pytest.raises(ValueError):
            parse_supercell_dimensions("0,1,1")  # Zero dimension
        with pytest.raises(ValueError):
            parse_supercell_dimensions("a,b,c")  # Non-numeric
    
    def test_load_default_settings(self):
        """Test loading default settings."""
        from vibroml.utils.utils import load_default_settings
        
        # This should work with the existing default_settings.json
        settings = load_default_settings()
        assert isinstance(settings, dict)
        
        # Should contain expected keys from actual file
        expected_keys = ["default_supercell_n", "screen_supercell_ns"]
        for key in expected_keys:
            assert key in settings
    
    def test_have_mace_constant(self):
        """Test HAVE_MACE constant exists."""
        from vibroml.utils.utils import HAVE_MACE
        assert isinstance(HAVE_MACE, bool)
    
    def test_clean_phonon_cache(self):
        """Test phonon cache cleaning."""
        from vibroml.utils.utils import clean_phonon_cache
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create fake phonon cache files
            cache_file = os.path.join(temp_dir, "phonopy.yaml")
            with open(cache_file, 'w') as f:
                f.write("test")
            
            # Function should run without error
            clean_phonon_cache(temp_dir)


class TestExistingStructureUtils:
    """Test existing structure utility functions."""
    
    def test_load_structure_function_exists(self):
        """Test that load_structure function exists and is callable."""
        from vibroml.utils.structure_utils import load_structure
        assert callable(load_structure)
    
    def test_initialize_calculator_function_exists(self):
        """Test that initialize_calculator function exists."""
        from vibroml.utils.structure_utils import initialize_calculator
        assert callable(initialize_calculator)
    
    def test_estimate_commensurate_supercell_size(self):
        """Test the existing commensurate supercell estimation."""
        from vibroml.utils.structure_utils import estimate_commensurate_supercell_size
        
        # Test Gamma point
        result = estimate_commensurate_supercell_size([0.0, 0.0, 0.0])
        assert result == (1, 1, 1)
        
        # Test simple fractions
        result = estimate_commensurate_supercell_size([0.5, 0.0, 0.0])
        assert result == (2, 1, 1)


class TestExistingPhononUtils:
    """Test existing phonon utility functions."""
    
    @patch('vibroml.utils.phonon_utils.Phonons')
    def test_run_phonon_calculation_exists(self, mock_phonons_class):
        """Test that run_phonon_calculation function works."""
        from vibroml.utils.phonon_utils import run_phonon_calculation
        
        mock_ph = Mock()
        mock_phonons_class.return_value = mock_ph
        
        atoms = bulk('Si')
        calculator = Mock()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            result = run_phonon_calculation(atoms, calculator, 3, 0.01, temp_dir)
            assert result == mock_ph
    
    @patch('vibroml.utils.phonon_utils.Phonons')
    def test_run_phonon_calculation_with_custom_supercell(self, mock_phonons_class):
        """Test our new custom supercell function."""
        from vibroml.utils.phonon_utils import run_phonon_calculation_with_custom_supercell
        
        mock_ph = Mock()
        mock_phonons_class.return_value = mock_ph
        
        atoms = bulk('Si')
        calculator = Mock()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            result = run_phonon_calculation_with_custom_supercell(
                atoms, calculator, (2, 3, 4), 0.01, temp_dir
            )
            
            assert result == mock_ph
            # Verify correct supercell was used
            call_args = mock_phonons_class.call_args
            assert call_args[1]['supercell'] == (2, 3, 4)
    
    def test_run_single_phonon_analysis_exists(self):
        """Test that run_single_phonon_analysis function exists."""
        from vibroml.utils.phonon_utils import run_single_phonon_analysis
        assert callable(run_single_phonon_analysis)


class TestExistingConfig:
    """Test existing configuration constants."""
    
    def test_conversion_factors(self):
        """Test conversion factors exist."""
        from vibroml.utils.config import EV_TO_THZ_FACTOR, THZ_TO_CM_FACTOR
        
        assert isinstance(EV_TO_THZ_FACTOR, float)
        assert isinstance(THZ_TO_CM_FACTOR, float)
        assert EV_TO_THZ_FACTOR > 0
        assert THZ_TO_CM_FACTOR > 0


class TestArgumentParsing:
    """Test command-line argument parsing."""
    
    @patch('vibroml.utils.utils.load_default_settings')
    def test_get_arg_parser_and_settings(self, mock_load_settings):
        """Test argument parser creation."""
        mock_load_settings.return_value = {
            "default_supercell_n": 3,
            "screen_supercell_ns": [2, 3, 4],
            "default_delta": 0.01,
            "default_fmax": 0.01,
            "default_engine": "mace",
            "default_model_name": "medium",
            "default_units": "THz",
            "phonon_path_npoints": 100,
            "phonon_dos_grid": [20, 20, 20],
            "default_traj_kT": 1.0,
            "negative_phonon_threshold_thz": -0.1,
            "screen_deltas": [0.05, 0.03, 0.01],
            "screen_fmax_values": [0.001, 0.0005, 0.0001],
            "soft_mode_max_iterations": 3,
            "soft_mode_displacement_scales": [0.25, 0.5, 1.0, 2.0, 4.0, 8.0],
            "mode2_ratio_scales": [-1.0, -0.5, -0.25, 0.0, 0.25, 0.5, 1.0],
            "soft_mode_num_top_structures_to_analyze": 3,
            "cell_scale_factors": [-0.05, 0.0, 0.05, 0.10],
            "num_modes_to_return": 2,
            "ga_population_size": 50,
            "ga_mutation_rate": 0.1,
            "num_new_points_per_iteration": 30,
            "default_method": "ga"
        }
        
        from vibroml.utils.utils import get_arg_parser_and_settings
        
        parser, settings = get_arg_parser_and_settings()
        assert parser is not None
        assert isinstance(settings, dict)
        
        # Test parsing new supercell argument
        args = parser.parse_args(['--cif', 'test.cif', '--supercell', '2,3,4'])
        assert args.supercell == '2,3,4'
        
        # Test backward compatibility
        args = parser.parse_args(['--cif', 'test.cif', '--supercell_n', '3'])
        assert args.supercell_n == 3


class TestSupercellIntegration:
    """Test integration of supercell modifications."""
    
    def test_supercell_parsing_integration(self):
        """Test that supercell parsing works end-to-end."""
        from vibroml.utils.utils import parse_supercell_dimensions
        
        test_cases = [
            ("3", (3, 3, 3)),
            ("2,3,4", (2, 3, 4)),
            ("1,1,2", (1, 1, 2)),
            (3, (3, 3, 3)),
            ([2, 3, 4], (2, 3, 4)),
            ((2, 3, 4), (2, 3, 4)),
        ]
        
        for input_val, expected in test_cases:
            result = parse_supercell_dimensions(input_val)
            assert result == expected, f"Failed for input {input_val}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

### File: test_screen_supercell_ns.py
"""Optimized tests for screen_supercell_ns argument parsing and functionality."""

import pytest
import os
import tempfile
import shutil
import subprocess
import json
from pathlib import Path

# Test configuration
CONDA_ENV_PATH = "/globalscratch/ucl/modl/rgouvea/vibroml_env/"

# Fast test parameters for screen_supercell_ns testing
FAST_SCREEN_PARAMS = [
    "--screen_deltas", "0.05",
    "--screen_fmax_values", "0.01",
    "--soft_mode_max_iterations", "1",
    "--no-relax"
]


class TestScreenSupercellNS:
    """Comprehensive tests for screen_supercell_ns argument parsing and functionality."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for test outputs."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def simple_cif_file(self, temp_dir):
        """Create a simple cubic test structure optimized for fast testing."""
        test_cif_content = """# Simple cubic test structure for screen_supercell_ns testing
data_SimpleCubic
_symmetry_space_group_name_H-M   'P 1'
_cell_length_a   2.50000000
_cell_length_b   2.50000000
_cell_length_c   2.50000000
_cell_angle_alpha   90.00000000
_cell_angle_beta   90.00000000
_cell_angle_gamma   90.00000000
_symmetry_Int_Tables_number   1
_chemical_formula_structural   LiF
_chemical_formula_sum   'Li1 F1'
_cell_volume   15.62500000
_cell_formula_units_Z   1
loop_
 _symmetry_equiv_pos_site_id
 _symmetry_equiv_pos_as_xyz
  1  'x, y, z'
loop_
 _atom_site_type_symbol
 _atom_site_label
 _atom_site_symmetry_multiplicity
 _atom_site_fract_x
 _atom_site_fract_y
 _atom_site_fract_z
 _atom_site_occupancy
  Li  Li0  1  0.00000000  0.00000000  0.00000000  1
  F   F1  1  0.50000000  0.50000000  0.50000000  1"""
        
        test_cif_path = os.path.join(temp_dir, "simple_cubic.cif")
        with open(test_cif_path, 'w') as f:
            f.write(test_cif_content)
        
        return test_cif_path
    
    def run_vibroml_command(self, args, cwd=None, timeout=300):
        """Run a vibroml command and return the result."""
        cmd = ["conda", "run", "-p", CONDA_ENV_PATH, "vibroml"] + args
        
        try:
            result = subprocess.run(
                cmd,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result
        except subprocess.TimeoutExpired:
            pytest.skip(f"Command timed out after {timeout} seconds")
        except FileNotFoundError:
            pytest.skip("vibroml command not found - conda environment may not be available")
    
    def test_screen_supercell_ns_single_value(self, simple_cif_file, temp_dir):
        """Test screen_supercell_ns with a single value."""
        os.chdir(temp_dir)
        
        args = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--auto",
            "--method", "traditional",
            "--screen_supercell_ns", "2",
            "--units", "THz"
        ] + FAST_SCREEN_PARAMS
        
        result = self.run_vibroml_command(args, cwd=temp_dir)
        
        # Check that command completed successfully
        assert result.returncode == 0, f"Command failed with stderr: {result.stderr}"
        
        # Check that output directory was created
        output_dirs = [d for d in os.listdir(temp_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) > 0, "No output directory created"
        
        output_dir = os.path.join(temp_dir, output_dirs[0])
        
        # Check that settings reflect the screen_supercell_ns value
        settings_file = os.path.join(output_dir, "initial_settings.json")
        assert os.path.exists(settings_file), "Initial settings file not found"
        
        with open(settings_file, 'r') as f:
            settings = json.load(f)
            assert "screen_supercell_ns" in settings
            # The value should be parsed as a list of supercell dimensions
            assert isinstance(settings["screen_supercell_ns"], list)
    
    def test_screen_supercell_ns_multiple_values(self, simple_cif_file, temp_dir):
        """Test screen_supercell_ns with multiple values."""
        os.chdir(temp_dir)
        
        args = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--auto",
            "--method", "traditional",
            "--screen_supercell_ns", "2", "3",  # Multiple values
            "--units", "THz"
        ] + FAST_SCREEN_PARAMS
        
        result = self.run_vibroml_command(args, cwd=temp_dir)
        
        # Check that command completed successfully
        assert result.returncode == 0, f"Command failed with stderr: {result.stderr}"
        
        # Check that output directory was created
        output_dirs = [d for d in os.listdir(temp_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) > 0, "No output directory created"
        
        output_dir = os.path.join(temp_dir, output_dirs[0])
        
        # Check that settings reflect the multiple screen_supercell_ns values
        settings_file = os.path.join(output_dir, "initial_settings.json")
        assert os.path.exists(settings_file), "Initial settings file not found"
        
        with open(settings_file, 'r') as f:
            settings = json.load(f)
            assert "screen_supercell_ns" in settings
            assert isinstance(settings["screen_supercell_ns"], list)
            assert len(settings["screen_supercell_ns"]) >= 2  # Should have at least 2 entries
    
    def test_screen_supercell_ns_anisotropic_format(self, simple_cif_file, temp_dir):
        """Test screen_supercell_ns with anisotropic supercell format."""
        os.chdir(temp_dir)
        
        args = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--auto",
            "--method", "traditional",
            "--screen_supercell_ns", "2,2,1", "1,1,2",  # Anisotropic supercells
            "--units", "THz"
        ] + FAST_SCREEN_PARAMS
        
        result = self.run_vibroml_command(args, cwd=temp_dir)
        
        # Check that command completed successfully
        assert result.returncode == 0, f"Command failed with stderr: {result.stderr}"
        
        # Check that output directory was created
        output_dirs = [d for d in os.listdir(temp_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) > 0, "No output directory created"
        
        output_dir = os.path.join(temp_dir, output_dirs[0])
        
        # Check that settings reflect the anisotropic screen_supercell_ns values
        settings_file = os.path.join(output_dir, "initial_settings.json")
        assert os.path.exists(settings_file), "Initial settings file not found"
        
        with open(settings_file, 'r') as f:
            settings = json.load(f)
            assert "screen_supercell_ns" in settings
            assert isinstance(settings["screen_supercell_ns"], list)
    
    def test_screen_supercell_ns_parameter_validation(self, simple_cif_file, temp_dir):
        """Test parameter validation for screen_supercell_ns."""
        os.chdir(temp_dir)
        
        # Test with invalid format (should handle gracefully or fail appropriately)
        args_invalid = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--auto",
            "--method", "traditional",
            "--screen_supercell_ns", "invalid",
            "--units", "THz"
        ] + FAST_SCREEN_PARAMS
        
        result = self.run_vibroml_command(args_invalid, cwd=temp_dir)
        
        # The behavior depends on implementation - it might fail or handle gracefully
        # We just check that it doesn't crash unexpectedly
        assert result.returncode in [0, 1, 2], "Unexpected return code for invalid screen_supercell_ns"
    
    def test_screen_supercell_ns_with_ga_method(self, simple_cif_file, temp_dir):
        """Test screen_supercell_ns with GA method."""
        os.chdir(temp_dir)
        
        args = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--auto",
            "--method", "ga",
            "--screen_supercell_ns", "2", "3",
            "--ga_population_size", "4",
            "--num_new_points_per_iteration", "2",
            "--units", "THz"
        ] + FAST_SCREEN_PARAMS
        
        result = self.run_vibroml_command(args, cwd=temp_dir, timeout=300)
        
        # Check that command completed successfully
        assert result.returncode == 0, f"GA method with screen_supercell_ns failed: {result.stderr}"
        
        # Check that output directory was created
        output_dirs = [d for d in os.listdir(temp_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) > 0, "No output directory created for GA method"
        
        output_dir = os.path.join(temp_dir, output_dirs[0])
        
        # Check that settings reflect both GA method and screen_supercell_ns
        settings_file = os.path.join(output_dir, "initial_settings.json")
        assert os.path.exists(settings_file), "Initial settings file not found for GA method"
        
        with open(settings_file, 'r') as f:
            settings = json.load(f)
            assert settings["method"] == "ga"
            assert "screen_supercell_ns" in settings


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])

### File: testssum.sh
#!/bin/bash

output_file="output"

# Clear the output file if it exists
> "$output_file"

# Process run_integration_tests.py first
echo "### File: run_integration_tests.py" >> "$output_file"
cat run_integration_tests.py >> "$output_file"
echo "" >> "$output_file" # Add a newline for separation

# Process all files starting with 'test'
for file in test*; do
    if [ -f "$file" ]; then # Ensure it's a regular file
        echo "### File: $file" >> "$output_file"
        cat "$file" >> "$output_file"
        echo "" >> "$output_file" # Add a newline for separation
    fi
done

echo "Concatenated files into '$output_file' with headers."

### File: test_units_functionality.py
"""Optimized tests for VibroML output units functionality (THz, cm-1, eV)."""

import pytest
import os
import tempfile
import shutil
import subprocess
import json
import re
from pathlib import Path

# Test configuration
CONDA_ENV_PATH = "/globalscratch/ucl/modl/rgouvea/vibroml_env/"

# Fast test parameters for units testing
FAST_UNITS_PARAMS = [
    "--supercell", "1,1,1",  # Minimal supercell for speed
    "--delta", "0.05",
    "--fmax", "0.01",
    "--no-relax"
]


class TestUnitsConversion:
    """Optimized tests for output units functionality with THz, cm-1, and eV."""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for test outputs."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def simple_cif_file(self, temp_dir):
        """Create a simple cubic test structure optimized for fast testing."""
        test_cif_content = """# Simple cubic test structure for units testing
data_SimpleCubic
_symmetry_space_group_name_H-M   'P 1'
_cell_length_a   2.50000000
_cell_length_b   2.50000000
_cell_length_c   2.50000000
_cell_angle_alpha   90.00000000
_cell_angle_beta   90.00000000
_cell_angle_gamma   90.00000000
_symmetry_Int_Tables_number   1
_chemical_formula_structural   LiF
_chemical_formula_sum   'Li1 F1'
_cell_volume   15.62500000
_cell_formula_units_Z   1
loop_
 _symmetry_equiv_pos_site_id
 _symmetry_equiv_pos_as_xyz
  1  'x, y, z'
loop_
 _atom_site_type_symbol
 _atom_site_label
 _atom_site_symmetry_multiplicity
 _atom_site_fract_x
 _atom_site_fract_y
 _atom_site_fract_z
 _atom_site_occupancy
  Li  Li0  1  0.00000000  0.00000000  0.00000000  1
  F   F1  1  0.50000000  0.50000000  0.50000000  1"""

        test_cif_path = os.path.join(temp_dir, "simple_cubic.cif")
        with open(test_cif_path, 'w') as f:
            f.write(test_cif_content)

        return test_cif_path

    def run_vibroml_command(self, args, cwd=None, timeout=300):
        """Run a vibroml command and return the result."""
        cmd = ["conda", "run", "-p", CONDA_ENV_PATH, "vibroml"] + args

        try:
            result = subprocess.run(
                cmd,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result
        except subprocess.TimeoutExpired:
            pytest.skip(f"Command timed out after {timeout} seconds")
        except FileNotFoundError:
            pytest.skip("vibroml command not found - conda environment may not be available")

    @pytest.mark.slow
    def test_units_comprehensive(self, simple_cif_file, temp_dir):
        """Comprehensive test for all supported units (THz, cm-1, eV) in a single optimized test."""
        units_to_test = ["THz", "cm-1", "eV"]

        for units in units_to_test:
            # Create subdirectory for each units test
            units_dir = os.path.join(temp_dir, f"test_{units}")
            os.makedirs(units_dir, exist_ok=True)
            os.chdir(units_dir)

            args = [
                "--cif", simple_cif_file,
                "--engine", "mace",
                "--units", units
            ] + FAST_UNITS_PARAMS

            result = self.run_vibroml_command(args, cwd=units_dir)

            # Check that command completed successfully
            assert result.returncode == 0, f"Command failed for units {units} with stderr: {result.stderr}"

            # Check that the specified units appear in the output
            assert units in result.stdout, f"{units} units not found in output"

            # Check that output directory was created
            output_dirs = [d for d in os.listdir(units_dir) if d.startswith("simple_cubic_phonon_output_")]
            assert len(output_dirs) > 0, f"No output directory created for {units}"

            output_dir = os.path.join(units_dir, output_dirs[0])

            # Check that settings reflect the correct units
            settings_file = os.path.join(output_dir, "initial_settings.json")
            assert os.path.exists(settings_file), f"Initial settings file not found for {units}"

            with open(settings_file, 'r') as f:
                settings = json.load(f)
                assert settings["units"] == units

    def test_units_conversion_consistency(self, simple_cif_file, temp_dir):
        """Test that units conversion is consistent and parameter validation works."""
        os.chdir(temp_dir)

        # Test with THz units (baseline)
        args_thz = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--units", "THz"
        ] + FAST_UNITS_PARAMS

        result_thz = self.run_vibroml_command(args_thz, cwd=temp_dir)
        assert result_thz.returncode == 0, f"THz test failed: {result_thz.stderr}"

        # Verify that settings are saved correctly
        output_dirs = [d for d in os.listdir(temp_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) > 0, "No output directory created"

        output_dir = os.path.join(temp_dir, output_dirs[0])
        settings_file = os.path.join(output_dir, "initial_settings.json")
        assert os.path.exists(settings_file), "Initial settings file not found"

        with open(settings_file, 'r') as f:
            settings = json.load(f)
            assert settings["units"] == "THz"

    def test_units_parameter_validation(self, simple_cif_file, temp_dir):
        """Test parameter validation for units functionality."""
        os.chdir(temp_dir)

        # Test invalid units (should fail or default to THz)
        args_invalid = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--units", "invalid_unit"
        ] + FAST_UNITS_PARAMS

        result = self.run_vibroml_command(args_invalid, cwd=temp_dir)
        # The behavior depends on implementation - it might fail or default to THz
        # We just check that it doesn't crash unexpectedly
        assert result.returncode in [0, 1, 2], "Unexpected return code for invalid units"

    def test_units_in_file_outputs(self, simple_cif_file, temp_dir):
        """Test that units are correctly reflected in output files."""
        os.chdir(temp_dir)

        # Test with cm-1 units to verify file output
        args = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--units", "cm-1"
        ] + FAST_UNITS_PARAMS

        result = self.run_vibroml_command(args, cwd=temp_dir)
        assert result.returncode == 0, f"Command failed: {result.stderr}"

        # Check that output directory was created
        output_dirs = [d for d in os.listdir(temp_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) > 0, "No output directory created"

        output_dir = os.path.join(temp_dir, output_dirs[0])

        # Check that settings file reflects the correct units
        settings_file = os.path.join(output_dir, "initial_settings.json")
        assert os.path.exists(settings_file), "Initial settings file not found"

        with open(settings_file, 'r') as f:
            settings = json.load(f)
            assert settings["units"] == "cm-1"

    @pytest.mark.slow
    def test_units_with_auto_mode(self, simple_cif_file, temp_dir):
        """Test that units work correctly with auto mode (optimized)."""
        os.chdir(temp_dir)

        # Test auto mode with cm-1 units
        args = [
            "--cif", simple_cif_file,
            "--engine", "mace",
            "--auto",
            "--method", "traditional",
            "--screen_supercell_ns", "2",  # Single supercell for speed
            "--screen_deltas", "0.05",
            "--screen_fmax_values", "0.01",
            "--soft_mode_max_iterations", "1",
            "--units", "cm-1"
        ] + FAST_UNITS_PARAMS

        result = self.run_vibroml_command(args, cwd=temp_dir, timeout=300)

        # Check that command completed successfully
        assert result.returncode == 0, f"Auto mode failed with stderr: {result.stderr}"

        # Check that the specified units appear in the output
        assert "cm-1" in result.stdout, "cm-1 units not found in auto mode output"

        # Check that output directory was created
        output_dirs = [d for d in os.listdir(temp_dir) if d.startswith("simple_cubic_phonon_output_")]
        assert len(output_dirs) > 0, "No output directory created for auto mode"

        output_dir = os.path.join(temp_dir, output_dirs[0])

        # Check that settings reflect the correct units
        settings_file = os.path.join(output_dir, "initial_settings.json")
        assert os.path.exists(settings_file), "Initial settings file not found for auto mode"

        with open(settings_file, 'r') as f:
            settings = json.load(f)
            assert settings["units"] == "cm-1"
            assert settings["auto"] is True


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])

### File: test_working_functions.py
"""Tests for functions that actually exist and work in VibroML."""

import pytest
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock


class TestActualSupercellFunctions:
    """Test the supercell functions we actually implemented."""
    
    def test_parse_supercell_dimensions(self):
        """Test the parse_supercell_dimensions function we added."""
        from vibroml.utils.utils import parse_supercell_dimensions
        
        # Test basic functionality
        assert parse_supercell_dimensions("3") == (3, 3, 3)
        assert parse_supercell_dimensions("2,3,4") == (2, 3, 4)
        assert parse_supercell_dimensions(3) == (3, 3, 3)
        assert parse_supercell_dimensions([2, 3, 4]) == (2, 3, 4)
        assert parse_supercell_dimensions((2, 3, 4)) == (2, 3, 4)
        
        # Test with spaces
        assert parse_supercell_dimensions("2, 3, 4") == (2, 3, 4)
        assert parse_supercell_dimensions(" 2 , 3 , 4 ") == (2, 3, 4)
        
        # Test error cases
        with pytest.raises(ValueError):
            parse_supercell_dimensions("1,2")  # Too few
        with pytest.raises(ValueError):
            parse_supercell_dimensions("1,2,3,4")  # Too many
        with pytest.raises(ValueError):
            parse_supercell_dimensions("a,b,c")  # Non-numeric
        with pytest.raises(ValueError):
            parse_supercell_dimensions("0,1,1")  # Zero
        with pytest.raises(ValueError):
            parse_supercell_dimensions("-1,2,3")  # Negative
    
    def test_generate_supercell_variants(self):
        """Test the generate_supercell_variants function we added."""
        from vibroml.utils.structure_utils import generate_supercell_variants
        
        # Test basic functionality
        variants = generate_supercell_variants((2, 2, 2), max_variants=3)
        
        assert isinstance(variants, list)
        assert len(variants) > 0
        assert len(variants) <= 3
        assert (2, 2, 2) in variants  # Original should be included
        
        # All variants should be valid tuples
        for variant in variants:
            assert isinstance(variant, tuple)
            assert len(variant) == 3
            assert all(isinstance(x, int) and x > 0 for x in variant)
    
    def test_estimate_commensurate_supercell_size_custom(self):
        """Test the custom commensurate supercell function we added."""
        from vibroml.utils.structure_utils import estimate_commensurate_supercell_size_custom
        
        # Test Gamma point
        result = estimate_commensurate_supercell_size_custom([0.0, 0.0, 0.0], (1, 1, 1))
        assert result == (1, 1, 1)
        
        # Test simple fractions
        result = estimate_commensurate_supercell_size_custom([0.5, 0.0, 0.0], (1, 1, 1))
        assert result == (2, 1, 1)
        
        # Test with base supercell
        result = estimate_commensurate_supercell_size_custom([0.5, 0.0, 0.0], (2, 2, 2))
        assert result == (4, 2, 2)


class TestExistingFunctions:
    """Test existing functions that we know work."""
    
    def test_load_default_settings(self):
        """Test that load_default_settings works."""
        from vibroml.utils.utils import load_default_settings
        
        settings = load_default_settings()
        assert isinstance(settings, dict)
        
        # Check for expected keys from the actual default_settings.json
        expected_keys = ["default_supercell_n", "screen_supercell_ns"]
        for key in expected_keys:
            assert key in settings
    
    def test_estimate_commensurate_supercell_size(self):
        """Test the original function."""
        from vibroml.utils.structure_utils import estimate_commensurate_supercell_size
        
        # Test Gamma point
        result = estimate_commensurate_supercell_size([0.0, 0.0, 0.0])
        assert result == (1, 1, 1)
        
        # Test simple fractions
        result = estimate_commensurate_supercell_size([0.5, 0.0, 0.0])
        assert result == (2, 1, 1)


class TestPhononFunctions:
    """Test phonon functions that actually exist."""
    
    @patch('vibroml.utils.phonon_utils.Phonons')
    def test_run_phonon_calculation_with_custom_supercell(self, mock_phonons_class):
        """Test our new custom supercell phonon function."""
        from vibroml.utils.phonon_utils import run_phonon_calculation_with_custom_supercell
        
        mock_ph = Mock()
        mock_phonons_class.return_value = mock_ph
        
        # Create mock atoms and calculator
        atoms = Mock()
        atoms.set_calculator = Mock()
        calculator = Mock()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            result = run_phonon_calculation_with_custom_supercell(
                atoms, calculator, (2, 3, 4), 0.01, temp_dir
            )
            
            assert result == mock_ph
            
            # Check that Phonons was called with correct supercell
            call_args = mock_phonons_class.call_args
            assert call_args[1]['supercell'] == (2, 3, 4)
            
            mock_ph.run.assert_called_once()
            mock_ph.read.assert_called_once_with(acoustic=True)
            mock_ph.clean.assert_called_once()
    
    @patch('vibroml.utils.phonon_utils.Phonons')
    def test_run_phonon_calculation_with_tuple_input(self, mock_phonons_class):
        """Test that run_phonon_calculation handles tuple input."""
        from vibroml.utils.phonon_utils import run_phonon_calculation
        
        mock_ph = Mock()
        mock_phonons_class.return_value = mock_ph
        
        atoms = Mock()
        atoms.set_calculator = Mock()
        calculator = Mock()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test with tuple input (should work with our modifications)
            result = run_phonon_calculation(atoms, calculator, (2, 3, 4), 0.01, temp_dir)
            
            assert result == mock_ph
            
            # Should use the tuple directly as supercell
            call_args = mock_phonons_class.call_args
            assert call_args[1]['supercell'] == (2, 3, 4)


class TestGeneticAlgorithmActual:
    """Test the actual GeneticAlgorithm class with correct parameters."""
    
    def test_genetic_algorithm_initialization(self):
        """Test GA initialization with actual parameters."""
        from vibroml.utils.genetic_algorithm import GeneticAlgorithm
        
        # Use actual parameters based on the real constructor
        ga = GeneticAlgorithm(
            population_size=10,
            mutation_rate=0.1,
            displacement_scale_bounds=(0.1, 2.0),
            ratio_mode2_to_mode1_bounds=(-1.0, 1.0),
            cell_scale_bounds=(-0.1, 0.1),
            cell_angle_bounds=(-5.0, 5.0),
            supercell_variants=[(2, 2, 2), (3, 3, 3)]
        )
        
        assert ga.population_size == 10
        assert ga.mutation_rate == 0.1
        assert ga.supercell_variants == [(2, 2, 2), (3, 3, 3)]
    
    def test_genetic_algorithm_generate_individual(self):
        """Test individual generation with actual GA class."""
        from vibroml.utils.genetic_algorithm import GeneticAlgorithm
        
        ga = GeneticAlgorithm(
            population_size=5,
            mutation_rate=0.1,
            displacement_scale_bounds=(0.1, 2.0),
            ratio_mode2_to_mode1_bounds=(-1.0, 1.0),
            cell_scale_bounds=(-0.1, 0.1),
            cell_angle_bounds=(-5.0, 5.0),
            supercell_variants=[(2, 2, 2)]
        )
        
        # Test the actual method name
        individual = ga._generate_random_individual()
        
        # Check the structure matches what the actual function returns
        assert isinstance(individual, tuple)
        assert len(individual) == 5  # Based on actual implementation


class TestConfigurationValues:
    """Test configuration constants."""
    
    def test_conversion_factors(self):
        """Test that conversion factors exist and are valid."""
        from vibroml.utils.config import EV_TO_THZ_FACTOR, THZ_TO_CM_FACTOR
        
        assert isinstance(EV_TO_THZ_FACTOR, float)
        assert isinstance(THZ_TO_CM_FACTOR, float)
        assert EV_TO_THZ_FACTOR > 0
        assert THZ_TO_CM_FACTOR > 0
    
    def test_have_mace_constant(self):
        """Test HAVE_MACE constant."""
        from vibroml.utils.utils import HAVE_MACE
        assert isinstance(HAVE_MACE, bool)


class TestArgumentParsing:
    """Test command line argument parsing."""
    
    @patch('vibroml.utils.utils.load_default_settings')
    def test_argument_parser_with_supercell(self, mock_load_settings):
        """Test that argument parser works with supercell arguments."""
        mock_load_settings.return_value = {
            "default_supercell_n": 3,
            "screen_supercell_ns": [2, 3, 4],
            "default_delta": 0.01,
            "default_fmax": 0.01,
            "default_engine": "mace",
            "default_model_name": "medium",
            "default_units": "THz",
            "phonon_path_npoints": 100,
            "phonon_dos_grid": [20, 20, 20],
            "default_traj_kT": 1.0,
            "negative_phonon_threshold_thz": -0.1,
            "screen_deltas": [0.05, 0.03, 0.01],
            "screen_fmax_values": [0.001, 0.0005, 0.0001],
            "soft_mode_max_iterations": 3,
            "soft_mode_displacement_scales": [0.25, 0.5, 1.0, 2.0, 4.0, 8.0],
            "mode2_ratio_scales": [-1.0, -0.5, -0.25, 0.0, 0.25, 0.5, 1.0],
            "soft_mode_num_top_structures_to_analyze": 3,
            "cell_scale_factors": [-0.05, 0.0, 0.05, 0.10],
            "num_modes_to_return": 2,
            "ga_population_size": 50,
            "ga_mutation_rate": 0.1,
            "num_new_points_per_iteration": 30,
            "default_method": "ga"
        }
        
        from vibroml.utils.utils import get_arg_parser_and_settings, parse_supercell_dimensions
        
        parser, settings = get_arg_parser_and_settings()
        
        # Test new supercell argument
        args = parser.parse_args(['--cif', 'test.cif', '--supercell', '2,3,4'])
        assert args.supercell == '2,3,4'
        
        # Test parsing
        parsed = parse_supercell_dimensions(args.supercell)
        assert parsed == (2, 3, 4)
        
        # Test backward compatibility
        args = parser.parse_args(['--cif', 'test.cif', '--supercell_n', '3'])
        assert args.supercell_n == 3
        assert args.supercell is None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

