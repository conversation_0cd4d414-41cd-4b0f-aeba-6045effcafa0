#!/usr/bin/env python
"""
Simple test script to verify the VibroML modifications work correctly.
This tests the core functionality without requiring full VibroML dependencies.
"""

import os
import tempfile
import shutil
import glob


def copy_structure_files_to_phonon_analysis_dir(phonon_analysis_dir, final_structures_dir, analysis_id):
    """
    Copy all structure file variants (CIF, XYZ, conventional, primitive) from final_structures/
    directory to the corresponding phonon analysis directory for better organization and traceability.
    """
    if not os.path.exists(final_structures_dir):
        print(f"  Warning: final_structures directory not found: {final_structures_dir}")
        return

    # Create a structures subdirectory in the phonon analysis directory
    structures_subdir = os.path.join(phonon_analysis_dir, "structure_files")
    os.makedirs(structures_subdir, exist_ok=True)

    # Find all structure files that match the analysis_id
    # The analysis_id format is like "top_1_energy_m6p0976", but we need to match files
    # that contain this pattern with underscores
    pattern = os.path.join(final_structures_dir, f"*{analysis_id.replace('_', '_')}*")
    matching_files = glob.glob(pattern)
    print(f"    Pattern: {pattern}")
    print(f"    Matching files: {matching_files}")

    # If no matches, try a more flexible approach
    if not matching_files:
        # Extract the energy part for more flexible matching
        if 'energy_' in analysis_id:
            energy_part = analysis_id.split('energy_')[1]
            pattern = os.path.join(final_structures_dir, f"*energy_{energy_part}*")
            matching_files = glob.glob(pattern)
            print(f"    Fallback pattern: {pattern}")
            print(f"    Fallback matching files: {matching_files}")

    copied_files = []
    for src_file in matching_files:
        if src_file.endswith(('.cif', '.xyz')):
            filename = os.path.basename(src_file)
            dest_file = os.path.join(structures_subdir, filename)
            try:
                shutil.copy2(src_file, dest_file)
                copied_files.append(filename)
                print(f"    Copied structure file: {filename}")
            except Exception as e:
                print(f"    Warning: Could not copy {filename}: {e}")

    if copied_files:
        print(f"  ✅ Copied {len(copied_files)} structure files to {structures_subdir}")
    else:
        print(f"  ⚠️  No matching structure files found for analysis_id: {analysis_id}")


def add_frequency_to_structure_filenames(final_structures_dir, analysis_id, softest_frequency, units="THz"):
    """
    Rename structure files in final_structures/ directory to include the softest phonon frequency.
    """
    if not os.path.exists(final_structures_dir):
        print(f"  Warning: final_structures directory not found: {final_structures_dir}")
        return

    # Format frequency for filename (replace . with p, - with m)
    freq_str = f"{abs(softest_frequency):.4f}".replace('.', 'p')
    if softest_frequency < 0:
        freq_str = 'm' + freq_str
    else:
        freq_str = 'p' + freq_str

    # Find all structure files that match the analysis_id
    pattern = os.path.join(final_structures_dir, f"*{analysis_id}*")
    matching_files = glob.glob(pattern)

    # If no matches, try a more flexible approach
    if not matching_files and 'energy_' in analysis_id:
        energy_part = analysis_id.split('energy_')[1]
        pattern = os.path.join(final_structures_dir, f"*energy_{energy_part}*")
        matching_files = glob.glob(pattern)

    renamed_files = []
    for src_file in matching_files:
        if src_file.endswith(('.cif', '.xyz')):
            # Check if frequency is already in filename
            if '_freq' in os.path.basename(src_file):
                print(f"    Frequency already in filename: {os.path.basename(src_file)}")
                continue

            # Create new filename with frequency
            base_name = os.path.basename(src_file)
            name_parts = base_name.rsplit('.', 1)  # Split filename and extension
            if len(name_parts) == 2:
                new_name = f"{name_parts[0]}_freq{freq_str}{units}.{name_parts[1]}"
            else:
                new_name = f"{base_name}_freq{freq_str}{units}"

            new_path = os.path.join(final_structures_dir, new_name)

            try:
                os.rename(src_file, new_path)
                renamed_files.append((os.path.basename(src_file), new_name))
                print(f"    Renamed: {os.path.basename(src_file)} → {new_name}")
            except Exception as e:
                print(f"    Warning: Could not rename {os.path.basename(src_file)}: {e}")

    if renamed_files:
        print(f"  ✅ Renamed {len(renamed_files)} structure files to include frequency ({softest_frequency:.4f} {units})")
    else:
        print(f"  ⚠️  No structure files renamed for analysis_id: {analysis_id}")


def test_copy_structure_files():
    """Test the copy_structure_files_to_phonon_analysis_dir function."""
    print("Testing copy_structure_files_to_phonon_analysis_dir...")

    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test directories
        final_structures_dir = os.path.join(temp_dir, "final_structures")
        phonon_analysis_dir = os.path.join(temp_dir, "final_phonon_analysis_top_1_energy_m6p0976")
        os.makedirs(final_structures_dir)
        os.makedirs(phonon_analysis_dir)

        # Create test structure files
        test_files = [
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976.cif",
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976.xyz",
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976_primitive.cif",
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976_conventional.cif"
        ]

        for filename in test_files:
            filepath = os.path.join(final_structures_dir, filename)
            with open(filepath, 'w') as f:
                f.write("# Test structure file\n")

        # Test the function
        analysis_id = "top_1_energy_m6p0976"
        print(f"  Looking for files matching: *{analysis_id}*")
        print(f"  Files in directory: {os.listdir(final_structures_dir)}")
        copy_structure_files_to_phonon_analysis_dir(phonon_analysis_dir, final_structures_dir, analysis_id)

        # Check if files were copied
        structures_subdir = os.path.join(phonon_analysis_dir, "structure_files")
        assert os.path.exists(structures_subdir), "structure_files subdirectory not created"

        copied_files = os.listdir(structures_subdir)
        assert len(copied_files) == 4, f"Expected 4 files, got {len(copied_files)}"

        print("  ✅ copy_structure_files_to_phonon_analysis_dir test passed")


def test_add_frequency_to_filenames():
    """Test the add_frequency_to_structure_filenames function."""
    print("Testing add_frequency_to_structure_filenames...")

    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test directory
        final_structures_dir = os.path.join(temp_dir, "final_structures")
        os.makedirs(final_structures_dir)

        # Create test structure files
        test_files = [
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976.cif",
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976.xyz",
            "unique_16_iter2_sample50_BaHfSe3_4.530_energy_m6p0339.cif"
        ]

        for filename in test_files:
            filepath = os.path.join(final_structures_dir, filename)
            with open(filepath, 'w') as f:
                f.write("# Test structure file\n")

        # Test with negative frequency
        analysis_id = "top_1_energy_m6p0976"
        softest_frequency = -0.0001
        add_frequency_to_structure_filenames(final_structures_dir, analysis_id, softest_frequency)

        # Check if files were renamed
        files_after = os.listdir(final_structures_dir)
        expected_renamed = [
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976_freqm0p0001THz.cif",
            "top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976_freqm0p0001THz.xyz"
        ]

        for expected_file in expected_renamed:
            assert expected_file in files_after, f"Expected renamed file {expected_file} not found"

        # Test with positive frequency
        analysis_id = "unique_16_energy_m6p0339"
        softest_frequency = 0.0025
        add_frequency_to_structure_filenames(final_structures_dir, analysis_id, softest_frequency)

        # Check if file was renamed
        files_after = os.listdir(final_structures_dir)
        expected_renamed = "unique_16_iter2_sample50_BaHfSe3_4.530_energy_m6p0339_freqp0p0025THz.cif"
        assert expected_renamed in files_after, f"Expected renamed file {expected_renamed} not found"

        print("  ✅ add_frequency_to_structure_filenames test passed")


def main():
    """Run all tests."""
    print("Testing VibroML modifications for structure organization and traceability")
    print("=" * 70)

    try:
        test_copy_structure_files()
        test_add_frequency_to_filenames()

        print("\n🎉 All tests passed!")
        print("\nModifications successfully implemented:")
        print("1. ✅ Copy structure files to phonon analysis directories")
        print("2. ✅ Include frequency information in final structure filenames")

        print("\nExample transformations:")
        print("Before: top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976.cif")
        print("After:  top_1_iter4_sample35_BaHfSe3_4.530_energy_m6p0976_freqm0p0001THz.cif")
        print("\nStructure files are now:")
        print("- Automatically copied to phonon analysis directories")
        print("- Named with frequency information for easy identification")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()