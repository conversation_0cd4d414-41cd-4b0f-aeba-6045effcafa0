name: /auto/globalscratch/users/r/g/rgouvea/vibroml_env
channels:
  - abinit
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1
  - _openmp_mutex=4.5
  - bzip2=1.0.8
  - ca-certificates=2025.6.15
  - ld_impl_linux-64=2.43
  - libffi=3.4.6
  - libgcc=15.1.0
  - libgcc-ng=15.1.0
  - libgomp=15.1.0
  - liblzma=5.8.1
  - liblzma-devel=5.8.1
  - libnsl=2.0.1
  - libsqlite=3.50.1
  - libuuid=2.38.1
  - libxcrypt=4.4.36
  - libzlib=1.3.1
  - ncurses=6.5
  - openssl=3.5.0
  - pip=25.1.1
  - python=3.9.20
  - readline=8.2
  - setuptools=80.9.0
  - tk=8.6.13
  - wheel=0.45.1
  - xz=5.8.1
  - xz-gpl-tools=5.8.1
  - xz-tools=5.8.1
  - pip:
    - absl-py==2.3.0
    - ase==3.25.0
    - astunparse==1.6.3
    - certifi==2025.6.15
    - charset-normalizer==3.4.2
    - configargparse==1.7.1
    - contourpy==1.3.0
    - cycler==0.12.1
    - e3nn==0.4.4
    - filelock==3.18.0
    - flatbuffers==25.2.10
    - fonttools==4.58.4
    - fsspec==2025.5.1
    - gast==0.6.0
    - gitdb==4.0.12
    - gitpython==3.1.44
    - google-pasta==0.2.0
    - grpcio==1.73.0
    - h5py==3.14.0
    - idna==3.10
    - importlib-metadata==8.7.0
    - importlib-resources==6.5.2
    - jinja2==3.1.6
    - joblib==1.5.1
    - keras==3.10.0
    - kiwisolver==1.4.7
    - latexcodec==3.0.1
    - libclang==18.1.1
    - lightning-utilities==0.14.3
    - lmdb==1.6.2
    - m3gnet==0.2.4
    - mace-torch==0.3.13
    - markdown==3.8.2
    - markdown-it-py==3.0.0
    - markupsafe==3.0.2
    - matplotlib==3.9.4
    - matscipy==1.1.1
    - mdurl==0.1.2
    - ml-dtypes==0.5.1
    - monty==2025.3.3
    - mpmath==1.3.0
    - namex==0.1.0
    - narwhals==1.44.0
    - networkx==3.2.1
    - numpy==1.26.4
    - nvidia-cublas-cu12==********
    - nvidia-cuda-cupti-cu12==12.6.80
    - nvidia-cuda-nvrtc-cu12==12.6.77
    - nvidia-cuda-runtime-cu12==12.6.77
    - nvidia-cudnn-cu12==********
    - nvidia-cufft-cu12==********
    - nvidia-cufile-cu12==********
    - nvidia-curand-cu12==*********
    - nvidia-cusolver-cu12==********
    - nvidia-cusparse-cu12==********
    - nvidia-cusparselt-cu12==0.6.3
    - nvidia-nccl-cu12==2.26.2
    - nvidia-nvjitlink-cu12==12.6.85
    - nvidia-nvtx-cu12==12.6.77
    - opt-einsum==3.4.0
    - opt-einsum-fx==0.1.4
    - optree==0.16.0
    - orjson==3.10.18
    - packaging==25.0
    - palettable==3.3.3
    - pandas==2.3.0
    - pillow==11.2.1
    - plotly==6.1.2
    - prettytable==3.16.0
    - protobuf==5.29.5
    - pybtex==0.25.0
    - pygments==2.19.2
    - pymatgen==2024.8.9
    - pyparsing==3.2.3
    - python-dateutil==2.9.0.post0
    - python-hostlist==2.2.1
    - pytz==2025.2
    - pyyaml==6.0.2
    - requests==2.32.4
    - rich==14.0.0
    - ruamel-yaml==0.18.14
    - ruamel-yaml-clib==0.2.12
    - scipy==1.13.1
    - six==1.17.0
    - smmap==5.0.2
    - spglib==2.6.0
    - sympy==1.14.0
    - tabulate==0.9.0
    - tensorboard==2.19.0
    - tensorboard-data-server==0.7.2
    - tensorflow==2.19.0
    - tensorflow-io-gcs-filesystem==0.37.1
    - termcolor==3.1.0
    - torch==2.7.1
    - torch-ema==0.3
    - torchmetrics==1.7.3
    - tqdm==4.67.1
    - triton==3.3.1
    - typing-extensions==4.14.0
    - tzdata==2025.2
    - uncertainties==3.2.3
    - urllib3==2.5.0
    - wcwidth==0.2.13
    - werkzeug==3.1.3
    - wrapt==1.17.2
    - zipp==3.23.0
prefix: /auto/globalscratch/users/r/g/rgouvea/vibroml_env
