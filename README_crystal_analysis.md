# Crystal Structure Analysis Script

This Python script performs high-throughput analysis of crystal structures using the pymatgen library. It's specifically designed for the VibroML project to analyze MEIDNet structures.

## Features

- **Batch Processing**: Analyzes all CIF files in specified directories
- **Space Group Analysis**: Determines space group symbol and number using SpacegroupAnalyzer
- **Conventional Cell**: Finds standardized conventional unit cell and counts atoms
- **Robust Error Handling**: Gracefully handles parsing errors and continues processing
- **Flexible Output**: Results can be written to CSV file or stdout
- **Progress Tracking**: Optional verbose mode with progress information

## Requirements

- Python 3.6+
- pymatgen library: `pip install pymatgen`

## Usage

### Basic Usage (output to console)
```bash
python crystal_structure_analysis.py
```

### Save results to CSV file
```bash
python crystal_structure_analysis.py --output results.csv
```

### Verbose mode with progress tracking
```bash
python crystal_structure_analysis.py --verbose --output results.csv
```

## Output Format

The script outputs CSV data with the following columns:

| Column | Description |
|--------|-------------|
| `filename` | Name of the structure file |
| `initial_atoms` | Number of atoms in the original input cell |
| `spacegroup_symbol` | Space group symbol (e.g., P-1, Pnma, P2_1/c) |
| `spacegroup_number` | International space group number (1-230) |
| `conventional_cell_atoms` | Number of atoms in the conventional standard cell |

## Example Output

```csv
filename,initial_atoms,spacegroup_symbol,spacegroup_number,conventional_cell_atoms
PEROVSK.unique_04_RbTaSe3_4.606_unique_13_energy_m5p5496_freq-0.0007THz.cif,80,P-1,2,20
PEROVSK.unique_155_BaHfSe3_4.530_top_1_energy_m6p0976_freq-0.0005THz.cif,20,P2_1/c,14,20
unique_68_KTaSe3_4.277_top_1_energy_m5p6247_freq-0.0012THz.cif,20,P2_1/c,14,20
```

## Directory Structure

The script is configured to analyze structures in:
- Main directory: `FINAL_deduplicated_optimized_MEIDNet_structures/`
- Subdirectory: `FIRST_TRY/`

It searches for files with extensions: `.cif`, `.CIF`, `.vasp`, `.POSCAR`, `.xyz`

## Error Handling

- Files that cannot be parsed are skipped with error messages printed to stderr
- The script continues processing remaining files even if some fail
- Final summary shows success/failure statistics

## Integration with VibroML

This script complements the existing VibroML codebase by providing:
- High-throughput structure analysis capabilities
- Standardized symmetry analysis using the same pymatgen library
- CSV output format suitable for further data analysis
- Consistent error handling patterns

## Command Line Options

```
usage: crystal_structure_analysis.py [-h] [--output OUTPUT] [--verbose]

optional arguments:
  -h, --help            show this help message and exit
  --output OUTPUT, -o OUTPUT
                        Output CSV file (default: stdout)
  --verbose, -v         Enable verbose output with progress information
```

## Performance

The script processes approximately 200 structure files in under a minute, with progress updates every 10 files in verbose mode.
