#!/bin/bash
#SBATCH --job-name=vibroml_phonons
#SBATCH --time=2:00:00
#SBATCH --output=log_phonons.txt
#SBATCH --nodes=1
#SBATCH --mem-per-cpu=40000
#SBATCH --partition=gpu
#SBATCH --gres=gpu:1

source ~/.bashrc
module load CUDA cuDNN/8.0.4.30-CUDA-11.1.1 
# TensorFlow/2.5.0-fosscuda-2020b
export XLA_FLAGS="--xla_gpu_cuda_data_dir=/home/<USER>/modl/rgouvea/anaconda3/envs/env_tfmodnet/lib/"
CUDA_DIR=/home/<USER>/modl/rgouvea/anaconda3/envs/env_tfmodnet/lib/python3.8/site-packages/nvidia/cuda_nvcc/


conda activate /auto/globalscratch/users/r/g/rgouvea/vibroml_env
#export PYTHONUSERBASE=intentionally-disabled  ##it was loading local modnet...
echo "start"
date

vibroml --cif CsPbI3.cif --auto > log_phonons.txt

echo "done"
date

